{"name": "laminas/laminas-loader", "description": "Autoloading and plugin loading strategies", "license": "BSD-3-<PERSON><PERSON>", "keywords": ["laminas", "loader"], "homepage": "https://laminas.dev", "support": {"docs": "https://docs.laminas.dev/laminas-loader/", "issues": "https://github.com/laminas/laminas-loader/issues", "source": "https://github.com/laminas/laminas-loader", "rss": "https://github.com/laminas/laminas-loader/releases.atom", "chat": "https://laminas.dev/chat", "forum": "https://discourse.laminas.dev"}, "config": {"sort-packages": true, "allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true}}, "require": {"php": "~8.0.0 || ~8.1.0 || ~8.2.0 || ~8.3.0"}, "require-dev": {"laminas/laminas-coding-standard": "~2.4.0", "phpunit/phpunit": "~9.5.25"}, "autoload": {"psr-4": {"Laminas\\Loader\\": "src/"}}, "autoload-dev": {"psr-4": {"LaminasTest\\Loader\\": "test/"}}, "scripts": {"check": ["@cs-check", "@test"], "cs-check": "phpcs", "cs-fix": "phpcbf", "test": "phpunit --colors=always", "test-coverage": "phpunit --colors=always --coverage-clover clover.xml"}, "conflict": {"zendframework/zend-loader": "*"}}