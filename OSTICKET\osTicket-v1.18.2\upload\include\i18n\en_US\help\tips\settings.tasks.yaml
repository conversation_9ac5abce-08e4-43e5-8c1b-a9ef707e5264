#
# This is popup help messages for the Admin Panel -> Settings -> Tasks
#
# Fields:
# title - Shown in bold at the top of the popover window
# content - The body of the help popover
# links - List of links shows below the content
#   title - Link title
#   href - href of link (links starting with / are translated to the
#       helpdesk installation path)
#
# The key names such as 'helpdesk_name' should not be translated as they
# must match the HTML #ids put into the page template.
#
---
number_format:
    title: Task Number Format
    content: >
        This setting is used to generate task numbers. Use hash signs
        (`#`) where digits are to be placed. Any other text in the number
        format will be preserved.
        <br/><br/>
        For example, for six-digit numbers, use <code>######</code>.

sequence_id:
    title: Task Number Sequence
    content: >
        Choose a sequence from which to derive new task numbers. The
        system has a incrementing sequence and a random sequence by default.
        You may create as many sequences as you wish.

default_sla:
    title: Default SLA
    content: >
        Choose the default Service Level Agreement to manage how long a task
        can remain Open before it is rendered Overdue.
    links:
      - title: Create more SLA Plans
        href: /scp/slas.php

default_priority:
    title: Default Priority
    content: >
        Choose a default <span class="doc-desc-title">priority</span> for
        tasks not assigned a priority automatically.

task_attachment_settings:
    title: Task Thread Attachments
    content: >
        Configure settings for files attached to the <span
        class="doc-desc-title">description</span> field. These settings
        are used for all new tasks and new messages regardless of the
        source channel (web portal, email, api, etc.).

page_title:
    title: Alerts and Notices
    content: >
        Alerts and Notices are automated email notifications sent to Agents
        when various task events are triggered.

task_alert:
    title: New Task Alert
    content: >
        <p>
        Alert sent out to Agents when a new task is created.
        </p><p class="info-banner">
        <i class="icon-info-sign"></i>
        This alert is not sent out if the task is auto-assigned.
        </p>
    links:
      - title: Default New Task Alert Template
        href: /scp/templates.php?default_for=task.alert

activity_alert:
    title: New Activity Alert
    content: >
        Alert sent out to Agents when a new message is appended to an
        existing task.
    links:
      - title: Default New Activity Alert Template
        href: /scp/templates.php?default_for=task.activity.alert

assignment_alert:
    title: Task Assignment Alert
    content: >
        Alert sent out to Agents on task assignment.
    links:
      - title: Default Task Assignment Alert Template
        href: /scp/templates.php?default_for=task.assignment.alert

transfer_alert:
    title: Task Transfer Alert
    content: >
        Alert sent out to Agents on task transfer between Departments.
    links:
      - title: Default Task Transfer Alert Template
        href: /scp/templates.php?default_for=task.transfer.alert

overdue_alert:
    title: Overdue Task Alert
    content: >
        Alert sent out to Agents when a task becomes overdue based on SLA
        or Due Date.
    links:
      - title: Default Stale Task Alert Template
        href: /scp/templates.php?default_for=task.overdue.alert

      - title: Manage SLAs
        href: /scp/slas.php

