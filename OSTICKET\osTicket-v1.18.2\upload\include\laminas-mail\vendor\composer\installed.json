{"packages": [{"name": "laminas/laminas-loader", "version": "2.10.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/laminas/laminas-loader.git", "reference": "e6fe952304ef40ce45cd814751ab35d42afdad12"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-loader/zipball/e6fe952304ef40ce45cd814751ab35d42afdad12", "reference": "e6fe952304ef40ce45cd814751ab35d42afdad12", "shasum": ""}, "require": {"php": "~8.0.0 || ~8.1.0 || ~8.2.0 || ~8.3.0"}, "conflict": {"zendframework/zend-loader": "*"}, "require-dev": {"laminas/laminas-coding-standard": "~2.4.0", "phpunit/phpunit": "~9.5.25"}, "time": "2023-10-18T09:58:51+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Laminas\\Loader\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Autoloading and plugin loading strategies", "homepage": "https://laminas.dev", "keywords": ["laminas", "loader"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-loader/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-loader/issues", "rss": "https://github.com/laminas/laminas-loader/releases.atom", "source": "https://github.com/laminas/laminas-loader"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "install-path": "../laminas/laminas-loader"}, {"name": "laminas/laminas-mime", "version": "2.12.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/laminas/laminas-mime.git", "reference": "08cc544778829b7d68d27a097885bd6e7130135e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-mime/zipball/08cc544778829b7d68d27a097885bd6e7130135e", "reference": "08cc544778829b7d68d27a097885bd6e7130135e", "shasum": ""}, "require": {"laminas/laminas-stdlib": "^2.7 || ^3.0", "php": "~8.0.0 || ~8.1.0 || ~8.2.0 || ~8.3.0"}, "conflict": {"zendframework/zend-mime": "*"}, "require-dev": {"laminas/laminas-coding-standard": "~2.4.0", "laminas/laminas-mail": "^2.19.0", "phpunit/phpunit": "~9.5.25"}, "suggest": {"laminas/laminas-mail": "Laminas\\Mail component"}, "time": "2023-11-02T16:47:19+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Laminas\\Mime\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Create and parse MIME messages and parts", "homepage": "https://laminas.dev", "keywords": ["laminas", "mime"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-mime/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-mime/issues", "rss": "https://github.com/laminas/laminas-mime/releases.atom", "source": "https://github.com/laminas/laminas-mime"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "install-path": "../laminas/laminas-mime"}, {"name": "laminas/laminas-servicemanager", "version": "3.22.1", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/laminas/laminas-servicemanager.git", "reference": "de98d297d4743956a0558a6d71616979ff779328"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-servicemanager/zipball/de98d297d4743956a0558a6d71616979ff779328", "reference": "de98d297d4743956a0558a6d71616979ff779328", "shasum": ""}, "require": {"laminas/laminas-stdlib": "^3.17", "php": "~8.1.0 || ~8.2.0 || ~8.3.0", "psr/container": "^1.0"}, "conflict": {"ext-psr": "*", "laminas/laminas-code": "<4.10.0", "zendframework/zend-code": "<3.3.1", "zendframework/zend-servicemanager": "*"}, "provide": {"psr/container-implementation": "^1.0"}, "replace": {"container-interop/container-interop": "^1.2.0"}, "require-dev": {"composer/package-versions-deprecated": "^*********", "friendsofphp/proxy-manager-lts": "^1.0.14", "laminas/laminas-code": "^4.10.0", "laminas/laminas-coding-standard": "~2.5.0", "laminas/laminas-container-config-test": "^0.8", "mikey179/vfsstream": "^1.6.11", "phpbench/phpbench": "^1.2.9", "phpunit/phpunit": "^10.4", "psalm/plugin-phpunit": "^0.18.4", "vimeo/psalm": "^5.8.0"}, "suggest": {"friendsofphp/proxy-manager-lts": "ProxyManager ^2.1.1 to handle lazy initialization of services"}, "time": "2023-10-24T11:19:47+00:00", "bin": ["bin/generate-deps-for-config-factory", "bin/generate-factory-for-class"], "type": "library", "installation-source": "dist", "autoload": {"files": ["src/autoload.php"], "psr-4": {"Laminas\\ServiceManager\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Factory-Driven Dependency Injection Container", "homepage": "https://laminas.dev", "keywords": ["PSR-11", "dependency-injection", "di", "dic", "laminas", "service-manager", "servicemanager"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-servicemanager/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-servicemanager/issues", "rss": "https://github.com/laminas/laminas-servicemanager/releases.atom", "source": "https://github.com/laminas/laminas-servicemanager"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "install-path": "../laminas/laminas-servicemanager"}, {"name": "laminas/laminas-stdlib", "version": "3.19.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/laminas/laminas-stdlib.git", "reference": "6a192dd0882b514e45506f533b833b623b78fff3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-stdlib/zipball/6a192dd0882b514e45506f533b833b623b78fff3", "reference": "6a192dd0882b514e45506f533b833b623b78fff3", "shasum": ""}, "require": {"php": "~8.1.0 || ~8.2.0 || ~8.3.0"}, "conflict": {"zendframework/zend-stdlib": "*"}, "require-dev": {"laminas/laminas-coding-standard": "^2.5", "phpbench/phpbench": "^1.2.15", "phpunit/phpunit": "^10.5.8", "psalm/plugin-phpunit": "^0.18.4", "vimeo/psalm": "^5.20.0"}, "time": "2024-01-19T12:39:49+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Laminas\\Stdlib\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "SPL extensions, array utilities, error handlers, and more", "homepage": "https://laminas.dev", "keywords": ["laminas", "stdlib"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-stdlib/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-stdlib/issues", "rss": "https://github.com/laminas/laminas-stdlib/releases.atom", "source": "https://github.com/laminas/laminas-stdlib"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "install-path": "../laminas/laminas-stdlib"}, {"name": "laminas/laminas-validator", "version": "2.55.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/laminas/laminas-validator.git", "reference": "dc3f2609d41b1e21bc24e3e147d7dd284e8a1556"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-validator/zipball/dc3f2609d41b1e21bc24e3e147d7dd284e8a1556", "reference": "dc3f2609d41b1e21bc24e3e147d7dd284e8a1556", "shasum": ""}, "require": {"laminas/laminas-servicemanager": "^3.21.0", "laminas/laminas-stdlib": "^3.13", "php": "~8.1.0 || ~8.2.0 || ~8.3.0", "psr/http-message": "^1.0.1 || ^2.0.0"}, "conflict": {"zendframework/zend-validator": "*"}, "require-dev": {"laminas/laminas-coding-standard": "^2.5", "laminas/laminas-db": "^2.20", "laminas/laminas-filter": "^2.35.2", "laminas/laminas-i18n": "^2.26.0", "laminas/laminas-session": "^2.20", "laminas/laminas-uri": "^2.11.0", "phpunit/phpunit": "^10.5.20", "psalm/plugin-phpunit": "^0.19.0", "psr/http-client": "^1.0.3", "psr/http-factory": "^1.1.0", "vimeo/psalm": "^5.24.0"}, "suggest": {"laminas/laminas-db": "Laminas\\Db component, required by the (No)RecordExists validator", "laminas/laminas-filter": "Laminas\\Filter component, required by the Digits validator", "laminas/laminas-i18n": "Laminas\\I18n component to allow translation of validation error messages", "laminas/laminas-i18n-resources": "Translations of validator messages", "laminas/laminas-servicemanager": "Laminas\\ServiceManager component to allow using the ValidatorPluginManager and validator chains", "laminas/laminas-session": "Laminas\\Session component, ^2.8; required by the Csrf validator", "laminas/laminas-uri": "Laminas\\Uri component, required by the Uri and Sitemap\\Loc validators", "psr/http-message": "psr/http-message, required when validating PSR-7 UploadedFileInterface instances via the Upload and UploadFile validators"}, "time": "2024-06-12T15:00:19+00:00", "type": "library", "extra": {"laminas": {"component": "Laminas\\Validator", "config-provider": "Laminas\\Validator\\ConfigProvider"}}, "installation-source": "dist", "autoload": {"psr-4": {"Laminas\\Validator\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Validation classes for a wide range of domains, and the ability to chain validators to create complex validation criteria", "homepage": "https://laminas.dev", "keywords": ["laminas", "validator"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-validator/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-validator/issues", "rss": "https://github.com/laminas/laminas-validator/releases.atom", "source": "https://github.com/laminas/laminas-validator"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "install-path": "../laminas/laminas-validator"}, {"name": "psr/container", "version": "1.1.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "513e0666f7216c7459170d56df27dfcefe1689ea"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/513e0666f7216c7459170d56df27dfcefe1689ea", "reference": "513e0666f7216c7459170d56df27dfcefe1689ea", "shasum": ""}, "require": {"php": ">=7.4.0"}, "time": "2021-11-05T16:50:12+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/1.1.2"}, "install-path": "../psr/container"}, {"name": "psr/http-message", "version": "2.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/402d35bcb92c70c026d1a6a9883f06b2ead23d71", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "time": "2023-04-04T09:54:51+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/2.0"}, "install-path": "../psr/http-message"}, {"name": "symfony/polyfill-intl-idn", "version": "v1.29.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-idn.git", "reference": "a287ed7475f85bf6f61890146edbc932c0fff919"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/a287ed7475f85bf6f61890146edbc932c0fff919", "reference": "a287ed7475f85bf6f61890146edbc932c0fff919", "shasum": ""}, "require": {"php": ">=7.1", "symfony/polyfill-intl-normalizer": "^1.10", "symfony/polyfill-php72": "^1.10"}, "suggest": {"ext-intl": "For best performance"}, "time": "2024-01-29T20:11:03+00:00", "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Idn\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's idn_to_ascii and idn_to_utf8 functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "idn", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-idn/tree/v1.29.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-intl-idn"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.29.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-normalizer.git", "reference": "bc45c394692b948b4d383a08d7753968bed9a83d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/bc45c394692b948b4d383a08d7753968bed9a83d", "reference": "bc45c394692b948b4d383a08d7753968bed9a83d", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-intl": "For best performance"}, "time": "2024-01-29T20:11:03+00:00", "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.29.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-intl-normalizer"}, {"name": "symfony/polyfill-mbstring", "version": "v1.29.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "9773676c8a1bb1f8d4340a62efe641cf76eda7ec"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/9773676c8a1bb1f8d4340a62efe641cf76eda7ec", "reference": "9773676c8a1bb1f8d4340a62efe641cf76eda7ec", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "time": "2024-01-29T20:11:03+00:00", "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.29.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-mbstring"}, {"name": "symfony/polyfill-php72", "version": "v1.29.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php72.git", "reference": "861391a8da9a04cbad2d232ddd9e4893220d6e25"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php72/zipball/861391a8da9a04cbad2d232ddd9e4893220d6e25", "reference": "861391a8da9a04cbad2d232ddd9e4893220d6e25", "shasum": ""}, "require": {"php": ">=7.1"}, "time": "2024-01-29T20:11:03+00:00", "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php72\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.2+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php72/tree/v1.29.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-php72"}, {"name": "webmozart/assert", "version": "1.11.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/webmozarts/assert.git", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozarts/assert/zipball/11cb2199493b2f8a3b53e7f19068fc6aac760991", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991", "shasum": ""}, "require": {"ext-ctype": "*", "php": "^7.2 || ^8.0"}, "conflict": {"phpstan/phpstan": "<0.12.20", "vimeo/psalm": "<4.6.1 || 4.6.2"}, "require-dev": {"phpunit/phpunit": "^8.5.13"}, "time": "2022-06-03T18:03:27+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.10-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Webmozart\\Assert\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Assertions to validate method input/output with nice error messages.", "keywords": ["assert", "check", "validate"], "support": {"issues": "https://github.com/webmozarts/assert/issues", "source": "https://github.com/webmozarts/assert/tree/1.11.0"}, "install-path": "../webmozart/assert"}], "dev": false, "dev-package-names": []}