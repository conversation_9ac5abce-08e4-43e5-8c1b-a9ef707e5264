#
# This is popup help messages for the Admin Panel -> Settings -> Agents
#
# Fields:
# title - Shown in bold at the top of the popover window
# content - The body of the help popover
# links - List of links shows below the content
#   title - Link title
#   href - href of link (links starting with / are translated to the
#       helpdesk installation path)
#
# The key names such as 'helpdesk_name' should not be translated as they
# must match the HTML #ids put into the page template.
#
---

# General Settings
agent_name_format:
    title: Agent Name Formatting
    content: >
        Choose a format for Agents names throughout the system. Email templates
        will use it for names if no other format is specified.

staff_identity_masking:
    title: Staff Identity Masking
    content: >
        If enabled, this will hide the Agent’s name from the Client during any
        communication.

disable_agent_collabs:
    title: Disable Agent Collaborators
    content: >
        If Enabled, Agents that are added as Collaborators by Users will be automatically
        Disabled. This is helpful when Users are blindly adding Agents to the CC field
        causing the Agents to receive all of the Participant Alerts.
        <br><br>
        <strong>Note:</strong>
        <br>
        This setting is global for all User created Tickets via API, Piping, and Fetching.

# Authentication settings
agent_password_policy:
    title: Password Management Policy
    content: >
        Chose a <span class="doc-desc-title">Password Policy</span> for <span class="doc-desc-title">Agents</span>.
        <br><br>
        Additional policies can be added by installing <span class="doc-desc-title">Password Policy</span> plugins.

allow_password_resets:
    title: Allow Password Resets
    content: >
        Enable this feature if you would like to display the
        <span class="doc-desc-title">Forgot My Password</span> link on the
        <span class="doc-desc-title">Staff Log-In Page</span>
        after a failed log in attempt.

reset_token_expiration:
    title: Password Reset Window
    content: >
        Choose the duration (in minutes) for which the <span class="doc-desc-title">
        Password Reset Tokens</span> will be valid. When an Agent requests a <span
        class="doc-desc-title">Password Reset</span>, they are emailed a token that
        will permit the reset to take place.

staff_session_timeout:
    title: Agent Session Timeout
    content: >
        Choose the maximum idle time (in minutes) before an Agent is required to
        log in again.
        <br><br>
        If you would like to disable <span class="doc-desc-title">Agent
        Session Timeouts</span>, enter 0.

bind_staff_session_to_ip:
    title: Bind Agent Session to IP
    content: >
        Enable this if you want Agent to be remembered by their current IP
        upon Log In.
        <br><br>
        This setting is not recommened for users assigned IP addresses dynamically.

require_agent_2fa:
     title: Require Two Factor Authentication
     content: >
         Enable this feature if you would like to have an extra layer of authentication
         set up for Agents when they log into the helpdesk. Once they correctly submit
         their username and password, they will be required to submit a token to finish
         logging into the helpdesk.
