html {
  font-size: 100%;
  overflow-y: scroll;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
}

body {
  margin: 0;
  font-size: 13px;
  line-height: 1.231;
  padding: 0;
}

body, input, select, textarea {
  font-family: sans-serif;
  color: #000;
}

b, strong {
  font-weight: bold;
}

blockquote {
  margin: 1em 40px;
}

hr {
  display: block;
  height: 1px;
  border: 0;
  border-top: 1px solid #ccc;
  margin: 1em 0;
  padding: 0;
}

small {
  font-size: 85%;
}

ul, ol {
  margin: 1em 0;
  padding: 0 0 0 30px;
}

img {
  border: 0;
  vertical-align: middle;
}

form {
  margin: 0;
}

fieldset {
  border: 0;
  margin: 0;
  padding: 0;
}

label {
  cursor: pointer;
}

input, select, textarea {
  font-size: 100%;
  margin: 0;
  vertical-align: baseline;
  *vertical-align: middle;
}

input {
  line-height: normal;
  *overflow: visible;
}

table input {
  *overflow: auto;
}

input[type="button"], input[type="reset"], input[type="submit"] {
  cursor: pointer;
  -webkit-appearance: button;
}

input[type="checkbox"], input[type="radio"] {
  box-sizing: border-box;
}

textarea {
  overflow: auto;
  vertical-align: top;
  resize: vertical;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

th, td {
  vertical-align: top;
}

th { text-align: left; font-weight: normal; }

h1, h2, h3, h4, h5, h6, form, fieldset {
  margin: 0;
  padding: 0;
}
