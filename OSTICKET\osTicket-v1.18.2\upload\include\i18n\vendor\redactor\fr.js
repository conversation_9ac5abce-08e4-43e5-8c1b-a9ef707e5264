(function($R)
{
    $R.lang['fr'] = {
        "format": "Format",
        "image": "Image",
        "file": "<PERSON>chier",
        "link": "Lien",
        "bold": "Gras",
        "italic": "Italique",
        "deleted": "Barré",
        "underline": "<PERSON><PERSON><PERSON>",
        "superscript": "Superscript",
        "subscript": "Subscript",
        "bold-abbr": "B",
        "italic-abbr": "I",
        "deleted-abbr": "S",
        "underline-abbr": "U",
        "superscript-abbr": "Sup",
        "subscript-abbr": "Sub",
        "lists": "Listes",
        "link-insert": "Insérer un lien",
        "link-edit": "Editer le lien",
        "link-in-new-tab": "Ouvrir le lien dans un nouvel onglet",
        "unlink": "Retirer le lien",
        "cancel": "Annuler",
        "close": "Ferm<PERSON>",
        "insert": "Insérer",
        "save": "<PERSON>uvegarder",
        "delete": "Supprimer",
        "text": "Texte",
        "edit": "Editer",
        "title": "Titre",
        "paragraph": "Texte normal",
        "quote": "Citation",
        "code": "Code",
        "heading1": "Titre 1",
        "heading2": "Titre 2",
        "heading3": "Titre 3",
        "heading4": "Titre 4",
        "heading5": "Titre 5",
        "heading6": "Titre 6",
        "filename": "Nom",
        "optional": "Optionnel",
        "unorderedlist": "Liste non-ordonnée",
        "orderedlist": "Liste ordonnée",
        "outdent": "Réduire le retrait",
        "indent": "Augmenter le retrait",
        "horizontalrule": "Ligne",
        "upload": "Upload",
        "upload-label": "Drop files here or click to upload",
        "upload-change-label": "Drop a new image to change",
        "accessibility-help-label": "Editeur de texte enrichi",
        "caption": "Caption",
        "bulletslist": "Bullets",
        "numberslist": "Numbers",
        "image-position": "Position",
        "none": "None",
        "left": "Left",
        "right": "Right",
        "center": "Center",
        "undo": "Undo",
        "redo": "Redo"
    };
})(Redactor);