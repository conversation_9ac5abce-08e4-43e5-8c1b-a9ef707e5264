{"name": "laminas/laminas-mime", "description": "Create and parse MIME messages and parts", "license": "BSD-3-<PERSON><PERSON>", "keywords": ["laminas", "mime"], "homepage": "https://laminas.dev", "support": {"docs": "https://docs.laminas.dev/laminas-mime/", "issues": "https://github.com/laminas/laminas-mime/issues", "source": "https://github.com/laminas/laminas-mime", "rss": "https://github.com/laminas/laminas-mime/releases.atom", "chat": "https://laminas.dev/chat", "forum": "https://discourse.laminas.dev"}, "config": {"sort-packages": true, "allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true}}, "require": {"php": "~8.0.0 || ~8.1.0 || ~8.2.0 || ~8.3.0", "laminas/laminas-stdlib": "^2.7 || ^3.0"}, "require-dev": {"laminas/laminas-coding-standard": "~2.4.0", "laminas/laminas-mail": "^2.19.0", "phpunit/phpunit": "~9.5.25"}, "suggest": {"laminas/laminas-mail": "Laminas\\Mail component"}, "autoload": {"psr-4": {"Laminas\\Mime\\": "src/"}}, "autoload-dev": {"files": ["test/TestAsset/Mail/Headers.php"], "psr-4": {"LaminasTest\\Mime\\": "test/"}}, "scripts": {"check": ["@cs-check", "@test"], "cs-check": "phpcs", "cs-fix": "phpcbf", "test": "phpunit --colors=always", "test-coverage": "phpunit --colors=always --coverage-clover clover.xml"}, "conflict": {"zendframework/zend-mime": "*"}}