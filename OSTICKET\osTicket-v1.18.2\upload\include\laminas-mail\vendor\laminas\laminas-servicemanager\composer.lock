{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "9ec9c9a8b4746d56a1f1cc10cc26afbb", "packages": [{"name": "laminas/laminas-stdlib", "version": "3.17.0", "source": {"type": "git", "url": "https://github.com/laminas/laminas-stdlib.git", "reference": "dd35c868075bad80b6718959740913e178eb4274"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-stdlib/zipball/dd35c868075bad80b6718959740913e178eb4274", "reference": "dd35c868075bad80b6718959740913e178eb4274", "shasum": ""}, "require": {"php": "~8.1.0 || ~8.2.0"}, "conflict": {"zendframework/zend-stdlib": "*"}, "require-dev": {"laminas/laminas-coding-standard": "^2.5", "phpbench/phpbench": "^1.2.9", "phpunit/phpunit": "^10.0.16", "psalm/plugin-phpunit": "^0.18.4", "vimeo/psalm": "^5.8"}, "type": "library", "autoload": {"psr-4": {"Laminas\\Stdlib\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "SPL extensions, array utilities, error handlers, and more", "homepage": "https://laminas.dev", "keywords": ["laminas", "stdlib"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-stdlib/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-stdlib/issues", "rss": "https://github.com/laminas/laminas-stdlib/releases.atom", "source": "https://github.com/laminas/laminas-stdlib"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2023-03-20T13:51:37+00:00"}, {"name": "psr/container", "version": "1.1.2", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "513e0666f7216c7459170d56df27dfcefe1689ea"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/513e0666f7216c7459170d56df27dfcefe1689ea", "reference": "513e0666f7216c7459170d56df27dfcefe1689ea", "shasum": ""}, "require": {"php": ">=7.4.0"}, "type": "library", "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/1.1.2"}, "time": "2021-11-05T16:50:12+00:00"}], "packages-dev": [{"name": "amphp/amp", "version": "v2.6.2", "source": {"type": "git", "url": "https://github.com/amphp/amp.git", "reference": "9d5100cebffa729aaffecd3ad25dc5aeea4f13bb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/amphp/amp/zipball/9d5100cebffa729aaffecd3ad25dc5aeea4f13bb", "reference": "9d5100cebffa729aaffecd3ad25dc5aeea4f13bb", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"amphp/php-cs-fixer-config": "dev-master", "amphp/phpunit-util": "^1", "ext-json": "*", "jetbrains/phpstorm-stubs": "^2019.3", "phpunit/phpunit": "^7 | ^8 | ^9", "psalm/phar": "^3.11@dev", "react/promise": "^2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "autoload": {"files": ["lib/functions.php", "lib/Internal/functions.php"], "psr-4": {"Amp\\": "lib"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A non-blocking concurrency framework for PHP applications.", "homepage": "https://amphp.org/amp", "keywords": ["async", "asynchronous", "awaitable", "concurrency", "event", "event-loop", "future", "non-blocking", "promise"], "support": {"irc": "irc://irc.freenode.org/amphp", "issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/v2.6.2"}, "funding": [{"url": "https://github.com/amphp", "type": "github"}], "time": "2022-02-20T17:52:18+00:00"}, {"name": "amphp/byte-stream", "version": "v1.8.1", "source": {"type": "git", "url": "https://github.com/amphp/byte-stream.git", "reference": "acbd8002b3536485c997c4e019206b3f10ca15bd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/amphp/byte-stream/zipball/acbd8002b3536485c997c4e019206b3f10ca15bd", "reference": "acbd8002b3536485c997c4e019206b3f10ca15bd", "shasum": ""}, "require": {"amphp/amp": "^2", "php": ">=7.1"}, "require-dev": {"amphp/php-cs-fixer-config": "dev-master", "amphp/phpunit-util": "^1.4", "friendsofphp/php-cs-fixer": "^2.3", "jetbrains/phpstorm-stubs": "^2019.3", "phpunit/phpunit": "^6 || ^7 || ^8", "psalm/phar": "^3.11.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"files": ["lib/functions.php"], "psr-4": {"Amp\\ByteStream\\": "lib"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A stream abstraction to make working with non-blocking I/O simple.", "homepage": "http://amphp.org/byte-stream", "keywords": ["amp", "amphp", "async", "io", "non-blocking", "stream"], "support": {"irc": "irc://irc.freenode.org/amphp", "issues": "https://github.com/amphp/byte-stream/issues", "source": "https://github.com/amphp/byte-stream/tree/v1.8.1"}, "funding": [{"url": "https://github.com/amphp", "type": "github"}], "time": "2021-03-30T17:13:30+00:00"}, {"name": "composer/package-versions-deprecated", "version": "*********", "source": {"type": "git", "url": "https://github.com/composer/package-versions-deprecated.git", "reference": "b4f54f74ef3453349c24a845d22392cd31e65f1d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/package-versions-deprecated/zipball/b4f54f74ef3453349c24a845d22392cd31e65f1d", "reference": "b4f54f74ef3453349c24a845d22392cd31e65f1d", "shasum": ""}, "require": {"composer-plugin-api": "^1.1.0 || ^2.0", "php": "^7 || ^8"}, "replace": {"ocramius/package-versions": "1.11.99"}, "require-dev": {"composer/composer": "^1.9.3 || ^2.0@dev", "ext-zip": "^1.13", "phpunit/phpunit": "^6.5 || ^7"}, "type": "composer-plugin", "extra": {"class": "PackageVersions\\Installer", "branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"PackageVersions\\": "src/PackageVersions"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be"}], "description": "Composer plugin that provides efficient querying for installed package versions (no runtime IO)", "support": {"issues": "https://github.com/composer/package-versions-deprecated/issues", "source": "https://github.com/composer/package-versions-deprecated/tree/*********"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2022-01-17T14:14:24+00:00"}, {"name": "composer/pcre", "version": "3.1.0", "source": {"type": "git", "url": "https://github.com/composer/pcre.git", "reference": "4bff79ddd77851fe3cdd11616ed3f92841ba5bd2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/pcre/zipball/4bff79ddd77851fe3cdd11616ed3f92841ba5bd2", "reference": "4bff79ddd77851fe3cdd11616ed3f92841ba5bd2", "shasum": ""}, "require": {"php": "^7.4 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.3", "phpstan/phpstan-strict-rules": "^1.1", "symfony/phpunit-bridge": "^5"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Composer\\Pcre\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "PCRE wrapping library that offers type-safe preg_* replacements.", "keywords": ["PCRE", "preg", "regex", "regular expression"], "support": {"issues": "https://github.com/composer/pcre/issues", "source": "https://github.com/composer/pcre/tree/3.1.0"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2022-11-17T09:50:14+00:00"}, {"name": "composer/semver", "version": "3.3.2", "source": {"type": "git", "url": "https://github.com/composer/semver.git", "reference": "3953f23262f2bff1919fc82183ad9acb13ff62c9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/semver/zipball/3953f23262f2bff1919fc82183ad9acb13ff62c9", "reference": "3953f23262f2bff1919fc82183ad9acb13ff62c9", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.4", "symfony/phpunit-bridge": "^4.2 || ^5"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Composer\\Semver\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://robbast.nl"}], "description": "Semver library that offers utilities, version constraint parsing and validation.", "keywords": ["semantic", "semver", "validation", "versioning"], "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/semver/issues", "source": "https://github.com/composer/semver/tree/3.3.2"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2022-04-01T19:23:25+00:00"}, {"name": "composer/xdebug-handler", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/composer/xdebug-handler.git", "reference": "ced299686f41dce890debac69273b47ffe98a40c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/xdebug-handler/zipball/ced299686f41dce890debac69273b47ffe98a40c", "reference": "ced299686f41dce890debac69273b47ffe98a40c", "shasum": ""}, "require": {"composer/pcre": "^1 || ^2 || ^3", "php": "^7.2.5 || ^8.0", "psr/log": "^1 || ^2 || ^3"}, "require-dev": {"phpstan/phpstan": "^1.0", "phpstan/phpstan-strict-rules": "^1.1", "symfony/phpunit-bridge": "^6.0"}, "type": "library", "autoload": {"psr-4": {"Composer\\XdebugHandler\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "john-s<PERSON><PERSON><PERSON>@blueyonder.co.uk"}], "description": "Restarts a process without Xdebug.", "keywords": ["Xdebug", "performance"], "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/xdebug-handler/issues", "source": "https://github.com/composer/xdebug-handler/tree/3.0.3"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2022-02-25T21:32:43+00:00"}, {"name": "dealerdirect/phpcodesniffer-composer-installer", "version": "v0.7.2", "source": {"type": "git", "url": "https://github.com/Dealerdirect/phpcodesniffer-composer-installer.git", "reference": "1c968e542d8843d7cd71de3c5c9c3ff3ad71a1db"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Dealerdirect/phpcodesniffer-composer-installer/zipball/1c968e542d8843d7cd71de3c5c9c3ff3ad71a1db", "reference": "1c968e542d8843d7cd71de3c5c9c3ff3ad71a1db", "shasum": ""}, "require": {"composer-plugin-api": "^1.0 || ^2.0", "php": ">=5.3", "squizlabs/php_codesniffer": "^2.0 || ^3.1.0 || ^4.0"}, "require-dev": {"composer/composer": "*", "php-parallel-lint/php-parallel-lint": "^1.3.1", "phpcompatibility/php-compatibility": "^9.0"}, "type": "composer-plugin", "extra": {"class": "Dealerdirect\\Composer\\Plugin\\Installers\\PHPCodeSniffer\\Plugin"}, "autoload": {"psr-4": {"Dealerdirect\\Composer\\Plugin\\Installers\\PHPCodeSniffer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.frenck.nl", "role": "Developer / IT Manager"}, {"name": "Contributors", "homepage": "https://github.com/Dealerdirect/phpcodesniffer-composer-installer/graphs/contributors"}], "description": "PHP_CodeSniffer Standards Composer Installer Plugin", "homepage": "http://www.dealerdirect.com", "keywords": ["PHPCodeSniffer", "PHP_CodeSniffer", "code quality", "codesniffer", "composer", "installer", "phpcbf", "phpcs", "plugin", "qa", "quality", "standard", "standards", "style guide", "stylecheck", "tests"], "support": {"issues": "https://github.com/dealerdirect/phpcodesniffer-composer-installer/issues", "source": "https://github.com/dealerdirect/phpcodesniffer-composer-installer"}, "time": "2022-02-04T12:51:07+00:00"}, {"name": "dnoegel/php-xdg-base-dir", "version": "v0.1.1", "source": {"type": "git", "url": "https://github.com/dnoegel/php-xdg-base-dir.git", "reference": "8f8a6e48c5ecb0f991c2fdcf5f154a47d85f9ffd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dnoegel/php-xdg-base-dir/zipball/8f8a6e48c5ecb0f991c2fdcf5f154a47d85f9ffd", "reference": "8f8a6e48c5ecb0f991c2fdcf5f154a47d85f9ffd", "shasum": ""}, "require": {"php": ">=5.3.2"}, "require-dev": {"phpunit/phpunit": "~7.0|~6.0|~5.0|~4.8.35"}, "type": "library", "autoload": {"psr-4": {"XdgBaseDir\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "implementation of xdg base directory specification for php", "support": {"issues": "https://github.com/dnoegel/php-xdg-base-dir/issues", "source": "https://github.com/dnoegel/php-xdg-base-dir/tree/v0.1.1"}, "time": "2019-12-04T15:06:13+00:00"}, {"name": "doctrine/annotations", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/doctrine/annotations.git", "reference": "e157ef3f3124bbf6fe7ce0ffd109e8a8ef284e7f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/annotations/zipball/e157ef3f3124bbf6fe7ce0ffd109e8a8ef284e7f", "reference": "e157ef3f3124bbf6fe7ce0ffd109e8a8ef284e7f", "shasum": ""}, "require": {"doctrine/lexer": "^2 || ^3", "ext-tokenizer": "*", "php": "^7.2 || ^8.0", "psr/cache": "^1 || ^2 || ^3"}, "require-dev": {"doctrine/cache": "^2.0", "doctrine/coding-standard": "^10", "phpstan/phpstan": "^1.8.0", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "symfony/cache": "^5.4 || ^6", "vimeo/psalm": "^4.10"}, "suggest": {"php": "PHP 8.0 or higher comes with attributes, a native replacement for annotations"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Annotations\\": "lib/Doctrine/Common/Annotations"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Docblock Annotations Parser", "homepage": "https://www.doctrine-project.org/projects/annotations.html", "keywords": ["annotations", "doc<PERSON>", "parser"], "support": {"issues": "https://github.com/doctrine/annotations/issues", "source": "https://github.com/doctrine/annotations/tree/2.0.1"}, "time": "2023-02-02T22:02:53+00:00"}, {"name": "doctrine/lexer", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/doctrine/lexer.git", "reference": "84a527db05647743d50373e0ec53a152f2cde568"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/lexer/zipball/84a527db05647743d50373e0ec53a152f2cde568", "reference": "84a527db05647743d50373e0ec53a152f2cde568", "shasum": ""}, "require": {"php": "^8.1"}, "require-dev": {"doctrine/coding-standard": "^10", "phpstan/phpstan": "^1.9", "phpunit/phpunit": "^9.5", "psalm/plugin-phpunit": "^0.18.3", "vimeo/psalm": "^5.0"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Lexer\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Lexer parser library that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "https://www.doctrine-project.org/projects/lexer.html", "keywords": ["annotations", "doc<PERSON>", "lexer", "parser", "php"], "support": {"issues": "https://github.com/doctrine/lexer/issues", "source": "https://github.com/doctrine/lexer/tree/3.0.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Flexer", "type": "tidelift"}], "time": "2022-12-15T16:57:16+00:00"}, {"name": "felixfbecker/advanced-json-rpc", "version": "v3.2.1", "source": {"type": "git", "url": "https://github.com/felixfbecker/php-advanced-json-rpc.git", "reference": "b5f37dbff9a8ad360ca341f3240dc1c168b45447"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/felixfbecker/php-advanced-json-rpc/zipball/b5f37dbff9a8ad360ca341f3240dc1c168b45447", "reference": "b5f37dbff9a8ad360ca341f3240dc1c168b45447", "shasum": ""}, "require": {"netresearch/jsonmapper": "^1.0 || ^2.0 || ^3.0 || ^4.0", "php": "^7.1 || ^8.0", "phpdocumentor/reflection-docblock": "^4.3.4 || ^5.0.0"}, "require-dev": {"phpunit/phpunit": "^7.0 || ^8.0"}, "type": "library", "autoload": {"psr-4": {"AdvancedJsonRpc\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["ISC"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A more advanced JSONRPC implementation", "support": {"issues": "https://github.com/felixfbecker/php-advanced-json-rpc/issues", "source": "https://github.com/felixfbecker/php-advanced-json-rpc/tree/v3.2.1"}, "time": "2021-06-11T22:34:44+00:00"}, {"name": "felixfbecker/language-server-protocol", "version": "v1.5.2", "source": {"type": "git", "url": "https://github.com/felixfbecker/php-language-server-protocol.git", "reference": "6e82196ffd7c62f7794d778ca52b69feec9f2842"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/felixfbecker/php-language-server-protocol/zipball/6e82196ffd7c62f7794d778ca52b69feec9f2842", "reference": "6e82196ffd7c62f7794d778ca52b69feec9f2842", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"phpstan/phpstan": "*", "squizlabs/php_codesniffer": "^3.1", "vimeo/psalm": "^4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"LanguageServerProtocol\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["ISC"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP classes for the Language Server Protocol", "keywords": ["language", "microsoft", "php", "server"], "support": {"issues": "https://github.com/felixfbecker/php-language-server-protocol/issues", "source": "https://github.com/felixfbecker/php-language-server-protocol/tree/v1.5.2"}, "time": "2022-03-02T22:36:06+00:00"}, {"name": "fidry/cpu-core-counter", "version": "0.5.1", "source": {"type": "git", "url": "https://github.com/theofidry/cpu-core-counter.git", "reference": "b58e5a3933e541dc286cc91fc4f3898bbc6f1623"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/theofidry/cpu-core-counter/zipball/b58e5a3933e541dc286cc91fc4f3898bbc6f1623", "reference": "b58e5a3933e541dc286cc91fc4f3898bbc6f1623", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"fidry/makefile": "^0.2.0", "phpstan/extension-installer": "^1.2.0", "phpstan/phpstan": "^1.9.2", "phpstan/phpstan-deprecation-rules": "^1.0.0", "phpstan/phpstan-phpunit": "^1.2.2", "phpstan/phpstan-strict-rules": "^1.4.4", "phpunit/phpunit": "^9.5.26 || ^8.5.31", "theofidry/php-cs-fixer-config": "^1.0", "webmozarts/strict-phpunit": "^7.5"}, "type": "library", "autoload": {"psr-4": {"Fidry\\CpuCoreCounter\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Théo FIDRY", "email": "<EMAIL>"}], "description": "Tiny utility to get the number of CPU cores.", "keywords": ["CPU", "core"], "support": {"issues": "https://github.com/theofidry/cpu-core-counter/issues", "source": "https://github.com/theofidry/cpu-core-counter/tree/0.5.1"}, "funding": [{"url": "https://github.com/theofidry", "type": "github"}], "time": "2022-12-24T12:35:10+00:00"}, {"name": "friendsofphp/proxy-manager-lts", "version": "v1.0.14", "source": {"type": "git", "url": "https://github.com/FriendsOfPHP/proxy-manager-lts.git", "reference": "a527c9d9d5348e012bd24482d83a5cd643bcbc9e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/FriendsOfPHP/proxy-manager-lts/zipball/a527c9d9d5348e012bd24482d83a5cd643bcbc9e", "reference": "a527c9d9d5348e012bd24482d83a5cd643bcbc9e", "shasum": ""}, "require": {"laminas/laminas-code": "~3.4.1|^4.0", "php": ">=7.1", "symfony/filesystem": "^4.4.17|^5.0|^6.0"}, "conflict": {"laminas/laminas-stdlib": "<3.2.1", "zendframework/zend-stdlib": "<3.2.1"}, "replace": {"ocramius/proxy-manager": "^2.1"}, "require-dev": {"ext-phar": "*", "symfony/phpunit-bridge": "^5.4|^6.0"}, "type": "library", "extra": {"thanks": {"name": "ocramius/proxy-manager", "url": "https://github.com/Ocramius/ProxyManager"}}, "autoload": {"psr-4": {"ProxyManager\\": "src/ProxyManager"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://ocramius.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Adding support for a wider range of PHP versions to ocramius/proxy-manager", "homepage": "https://github.com/FriendsOfPHP/proxy-manager-lts", "keywords": ["aop", "lazy loading", "proxy", "proxy pattern", "service proxies"], "support": {"issues": "https://github.com/FriendsOfPHP/proxy-manager-lts/issues", "source": "https://github.com/FriendsOfPHP/proxy-manager-lts/tree/v1.0.14"}, "funding": [{"url": "https://github.com/Ocramius", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/ocramius/proxy-manager", "type": "tidelift"}], "time": "2023-01-30T10:40:19+00:00"}, {"name": "laminas/laminas-code", "version": "4.10.0", "source": {"type": "git", "url": "https://github.com/laminas/laminas-code.git", "reference": "ad8b36073f9ac792716478befadca0798cc15635"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-code/zipball/ad8b36073f9ac792716478befadca0798cc15635", "reference": "ad8b36073f9ac792716478befadca0798cc15635", "shasum": ""}, "require": {"php": "~8.1.0 || ~8.2.0"}, "require-dev": {"doctrine/annotations": "^2.0.0", "ext-phar": "*", "laminas/laminas-coding-standard": "^2.3.0", "laminas/laminas-stdlib": "^3.6.1", "phpunit/phpunit": "^10.0.9", "psalm/plugin-phpunit": "^0.18.4", "vimeo/psalm": "^5.7.1"}, "suggest": {"doctrine/annotations": "Doctrine\\Common\\Annotations >=1.0 for annotation features", "laminas/laminas-stdlib": "Laminas\\Stdlib component"}, "type": "library", "autoload": {"psr-4": {"Laminas\\Code\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Extensions to the PHP Reflection API, static code scanning, and code generation", "homepage": "https://laminas.dev", "keywords": ["code", "laminas", "laminasframework"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-code/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-code/issues", "rss": "https://github.com/laminas/laminas-code/releases.atom", "source": "https://github.com/laminas/laminas-code"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2023-03-08T11:55:01+00:00"}, {"name": "laminas/laminas-coding-standard", "version": "2.5.0", "source": {"type": "git", "url": "https://github.com/laminas/laminas-coding-standard.git", "reference": "c1aaa18a7c860c6932677a3e4ec13161f9fc7d61"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-coding-standard/zipball/c1aaa18a7c860c6932677a3e4ec13161f9fc7d61", "reference": "c1aaa18a7c860c6932677a3e4ec13161f9fc7d61", "shasum": ""}, "require": {"dealerdirect/phpcodesniffer-composer-installer": "^0.7 || ^1.0", "php": "^7.4 || ^8.0", "slevomat/coding-standard": "^7.0", "squizlabs/php_codesniffer": "^3.6", "webimpress/coding-standard": "^1.2"}, "conflict": {"phpstan/phpdoc-parser": ">=1.6.0"}, "type": "phpcodesniffer-standard", "autoload": {"psr-4": {"LaminasCodingStandard\\": "src/LaminasCodingStandard/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Laminas Coding Standard", "homepage": "https://laminas.dev", "keywords": ["Coding Standard", "laminas"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-coding-standard/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-coding-standard/issues", "rss": "https://github.com/laminas/laminas-coding-standard/releases.atom", "source": "https://github.com/laminas/laminas-coding-standard"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2023-01-05T15:53:40+00:00"}, {"name": "laminas/laminas-container-config-test", "version": "0.8.0", "source": {"type": "git", "url": "https://github.com/laminas/laminas-container-config-test.git", "reference": "06474faed18a2732b21355297fa8c56f1aff2e91"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-container-config-test/zipball/06474faed18a2732b21355297fa8c56f1aff2e91", "reference": "06474faed18a2732b21355297fa8c56f1aff2e91", "shasum": ""}, "require": {"php": "~8.0.0 || ~8.1.0 || ~8.2.0", "psr/container": "^1.0 || ^2.0"}, "conflict": {"zendframework/zend-container-config-test": "*"}, "require-dev": {"laminas/laminas-coding-standard": "^2.3", "phpunit/phpunit": "^9.5.26", "psalm/plugin-phpunit": "^0.18.0", "vimeo/psalm": "^4.29.0"}, "type": "library", "autoload": {"files": ["src/TestAsset/function-factory.php", "src/TestAsset/function-factory-with-name.php", "src/TestAsset/function-factory.legacy.php", "src/TestAsset/function-factory-with-name.legacy.php"], "psr-4": {"Laminas\\ContainerConfigTest\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Mezzio PSR-11 container configuration tests", "homepage": "https://laminas.dev", "keywords": ["PSR-11", "container", "laminas", "mezzio", "test"], "support": {"chat": "https://laminas.dev/chat", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-container-config-test/issues", "rss": "https://github.com/laminas/laminas-container-config-test/releases.atom", "source": "https://github.com/laminas/laminas-container-config-test"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2022-11-16T00:42:21+00:00"}, {"name": "mikey179/vfsstream", "version": "v1.6.11", "source": {"type": "git", "url": "https://github.com/bovigo/vfsStream.git", "reference": "17d16a85e6c26ce1f3e2fa9ceeacdc2855db1e9f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/bovigo/vfsStream/zipball/17d16a85e6c26ce1f3e2fa9ceeacdc2855db1e9f", "reference": "17d16a85e6c26ce1f3e2fa9ceeacdc2855db1e9f", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "^4.5|^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.6.x-dev"}}, "autoload": {"psr-0": {"org\\bovigo\\vfs\\": "src/main/php"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "homepage": "http://frankkleine.de/", "role": "Developer"}], "description": "Virtual file system to mock the real file system in unit tests.", "homepage": "http://vfs.bovigo.org/", "support": {"issues": "https://github.com/bovigo/vfsStream/issues", "source": "https://github.com/bovigo/vfsStream/tree/master", "wiki": "https://github.com/bovigo/vfsStream/wiki"}, "time": "2022-02-23T02:02:42+00:00"}, {"name": "myclabs/deep-copy", "version": "1.11.1", "source": {"type": "git", "url": "https://github.com/myclabs/DeepCopy.git", "reference": "7284c22080590fb39f2ffa3e9057f10a4ddd0e0c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/7284c22080590fb39f2ffa3e9057f10a4ddd0e0c", "reference": "7284c22080590fb39f2ffa3e9057f10a4ddd0e0c", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"doctrine/collections": "<1.6.8", "doctrine/common": "<2.13.3 || >=3,<3.2.2"}, "require-dev": {"doctrine/collections": "^1.6.8", "doctrine/common": "^2.13.3 || ^3.2.2", "phpunit/phpunit": "^7.5.20 || ^8.5.23 || ^9.5.13"}, "type": "library", "autoload": {"files": ["src/DeepCopy/deep_copy.php"], "psr-4": {"DeepCopy\\": "src/DeepCopy/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Create deep copies (clones) of your objects", "keywords": ["clone", "copy", "duplicate", "object", "object graph"], "support": {"issues": "https://github.com/myclabs/DeepCopy/issues", "source": "https://github.com/myclabs/DeepCopy/tree/1.11.1"}, "funding": [{"url": "https://tidelift.com/funding/github/packagist/myclabs/deep-copy", "type": "tidelift"}], "time": "2023-03-08T13:26:56+00:00"}, {"name": "netresearch/jsonmapper", "version": "v4.2.0", "source": {"type": "git", "url": "https://github.com/cweiske/jsonmapper.git", "reference": "f60565f8c0566a31acf06884cdaa591867ecc956"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cweiske/jsonmapper/zipball/f60565f8c0566a31acf06884cdaa591867ecc956", "reference": "f60565f8c0566a31acf06884cdaa591867ecc956", "shasum": ""}, "require": {"ext-json": "*", "ext-pcre": "*", "ext-reflection": "*", "ext-spl": "*", "php": ">=7.1"}, "require-dev": {"phpunit/phpunit": "~7.5 || ~8.0 || ~9.0", "squizlabs/php_codesniffer": "~3.5"}, "type": "library", "autoload": {"psr-0": {"JsonMapper": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["OSL-3.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://github.com/cweiske/jsonmapper/", "role": "Developer"}], "description": "Map nested JSON structures onto PHP classes", "support": {"email": "<EMAIL>", "issues": "https://github.com/cweiske/jsonmapper/issues", "source": "https://github.com/cweiske/jsonmapper/tree/v4.2.0"}, "time": "2023-04-09T17:37:40+00:00"}, {"name": "nikic/php-parser", "version": "v4.17.1", "source": {"type": "git", "url": "https://github.com/nikic/PHP-Parser.git", "reference": "a6303e50c90c355c7eeee2c4a8b27fe8dc8fef1d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/PHP-Parser/zipball/a6303e50c90c355c7eeee2c4a8b27fe8dc8fef1d", "reference": "a6303e50c90c355c7eeee2c4a8b27fe8dc8fef1d", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": ">=7.0"}, "require-dev": {"ircmaxell/php-yacc": "^0.0.7", "phpunit/phpunit": "^6.5 || ^7.0 || ^8.0 || ^9.0"}, "bin": ["bin/php-parse"], "type": "library", "extra": {"branch-alias": {"dev-master": "4.9-dev"}}, "autoload": {"psr-4": {"PhpParser\\": "lib/Php<PERSON><PERSON>er"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>"}], "description": "A PHP parser written in PHP", "keywords": ["parser", "php"], "support": {"issues": "https://github.com/nikic/PHP-Parser/issues", "source": "https://github.com/nikic/PHP-Parser/tree/v4.17.1"}, "time": "2023-08-13T19:53:39+00:00"}, {"name": "phar-io/manifest", "version": "2.0.3", "source": {"type": "git", "url": "https://github.com/phar-io/manifest.git", "reference": "97803eca37d319dfa7826cc2437fc020857acb53"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/manifest/zipball/97803eca37d319dfa7826cc2437fc020857acb53", "reference": "97803eca37d319dfa7826cc2437fc020857acb53", "shasum": ""}, "require": {"ext-dom": "*", "ext-phar": "*", "ext-xmlwriter": "*", "phar-io/version": "^3.0.1", "php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Component for reading phar.io manifest information from a PHP Archive (PHAR)", "support": {"issues": "https://github.com/phar-io/manifest/issues", "source": "https://github.com/phar-io/manifest/tree/2.0.3"}, "time": "2021-07-20T11:28:43+00:00"}, {"name": "phar-io/version", "version": "3.2.1", "source": {"type": "git", "url": "https://github.com/phar-io/version.git", "reference": "4f7fd7836c6f332bb2933569e566a0d6c4cbed74"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/version/zipball/4f7fd7836c6f332bb2933569e566a0d6c4cbed74", "reference": "4f7fd7836c6f332bb2933569e566a0d6c4cbed74", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Library for handling version information and constraints", "support": {"issues": "https://github.com/phar-io/version/issues", "source": "https://github.com/phar-io/version/tree/3.2.1"}, "time": "2022-02-21T01:04:05+00:00"}, {"name": "phpbench/container", "version": "2.2.1", "source": {"type": "git", "url": "https://github.com/phpbench/container.git", "reference": "6d555ff7174fca13f9b1ec0b4a089ed41d0ab392"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpbench/container/zipball/6d555ff7174fca13f9b1ec0b4a089ed41d0ab392", "reference": "6d555ff7174fca13f9b1ec0b4a089ed41d0ab392", "shasum": ""}, "require": {"psr/container": "^1.0|^2.0", "symfony/options-resolver": "^4.2 || ^5.0 || ^6.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.16", "phpstan/phpstan": "^0.12.52", "phpunit/phpunit": "^8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "autoload": {"psr-4": {"PhpBench\\DependencyInjection\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Simple, configurable, service container.", "support": {"issues": "https://github.com/phpbench/container/issues", "source": "https://github.com/phpbench/container/tree/2.2.1"}, "time": "2022-01-25T10:17:35+00:00"}, {"name": "phpbench/dom", "version": "0.3.3", "source": {"type": "git", "url": "https://github.com/phpbench/dom.git", "reference": "786a96db538d0def931f5b19225233ec42ec7a72"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpbench/dom/zipball/786a96db538d0def931f5b19225233ec42ec7a72", "reference": "786a96db538d0def931f5b19225233ec42ec7a72", "shasum": ""}, "require": {"ext-dom": "*", "php": "^7.3||^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.14", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^8.0||^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-4": {"PhpBench\\Dom\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "DOM wrapper to simplify working with the PHP DOM implementation", "support": {"issues": "https://github.com/phpbench/dom/issues", "source": "https://github.com/phpbench/dom/tree/0.3.3"}, "time": "2023-03-06T23:46:57+00:00"}, {"name": "phpbench/phpbench", "version": "1.2.10", "source": {"type": "git", "url": "https://github.com/phpbench/phpbench.git", "reference": "95206f92479674599a75e02b74b9933e2d9883aa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpbench/phpbench/zipball/95206f92479674599a75e02b74b9933e2d9883aa", "reference": "95206f92479674599a75e02b74b9933e2d9883aa", "shasum": ""}, "require": {"doctrine/annotations": "^1.13 || ^2.0", "ext-dom": "*", "ext-json": "*", "ext-pcre": "*", "ext-reflection": "*", "ext-spl": "*", "ext-tokenizer": "*", "php": "^7.4 || ^8.0", "phpbench/container": "^2.1", "phpbench/dom": "~0.3.3", "psr/log": "^1.1 || ^2.0 || ^3.0", "seld/jsonlint": "^1.1", "symfony/console": "^4.2 || ^5.0  || ^6.0", "symfony/filesystem": "^4.2 || ^5.0 || ^6.0", "symfony/finder": "^4.2 || ^5.0 || ^6.0", "symfony/options-resolver": "^4.2 || ^5.0 || ^6.0", "symfony/process": "^4.2 || ^5.0 || ^6.0", "webmozart/glob": "^4.6"}, "require-dev": {"dantleech/invoke": "^2.0", "friendsofphp/php-cs-fixer": "^3.0", "jangregor/phpstan-prophecy": "^1.0", "phpspec/prophecy": "^1.12", "phpstan/extension-installer": "^1.1", "phpstan/phpstan": "^1.0", "phpstan/phpstan-phpunit": "^1.0", "phpunit/phpunit": "^9.0", "symfony/error-handler": "^5.2 || ^6.0", "symfony/var-dumper": "^4.0 || ^5.0 || ^6.0"}, "suggest": {"ext-xdebug": "For Xdebug profiling extension."}, "bin": ["bin/phpbench"], "type": "library", "extra": {"branch-alias": {"dev-master": "1.2-dev"}}, "autoload": {"files": ["lib/Report/Func/functions.php"], "psr-4": {"PhpBench\\": "lib/", "PhpBench\\Extensions\\XDebug\\": "extensions/xdebug/lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Benchmarking Framework", "support": {"issues": "https://github.com/phpbench/phpbench/issues", "source": "https://github.com/phpbench/phpbench/tree/1.2.10"}, "funding": [{"url": "https://github.com/dantleech", "type": "github"}], "time": "2023-03-24T08:52:55+00:00"}, {"name": "phpdocumentor/reflection-common", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionCommon.git", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionCommon/zipball/1d01c49d4ed62f25aa84a747ad35d5a16924662b", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-2.x": "2.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common reflection classes used by phpdocumentor to reflect the code structure", "homepage": "http://www.phpdoc.org", "keywords": ["FQSEN", "phpDocumentor", "phpdoc", "reflection", "static analysis"], "support": {"issues": "https://github.com/phpDocumentor/ReflectionCommon/issues", "source": "https://github.com/phpDocumentor/ReflectionCommon/tree/2.x"}, "time": "2020-06-27T09:03:43+00:00"}, {"name": "phpdocumentor/reflection-docblock", "version": "5.3.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "reference": "622548b623e81ca6d78b721c5e029f4ce664f170"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/622548b623e81ca6d78b721c5e029f4ce664f170", "reference": "622548b623e81ca6d78b721c5e029f4ce664f170", "shasum": ""}, "require": {"ext-filter": "*", "php": "^7.2 || ^8.0", "phpdocumentor/reflection-common": "^2.2", "phpdocumentor/type-resolver": "^1.3", "webmozart/assert": "^1.9.1"}, "require-dev": {"mockery/mockery": "~1.3.2", "psalm/phar": "^4.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "With this component, a library can provide support for annotations via DocBlocks or otherwise retrieve information that is embedded in a DocBlock.", "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/5.3.0"}, "time": "2021-10-19T17:43:47+00:00"}, {"name": "phpdocumentor/type-resolver", "version": "1.6.2", "source": {"type": "git", "url": "https://github.com/phpDocumentor/TypeResolver.git", "reference": "48f445a408c131e38cab1c235aa6d2bb7a0bb20d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/48f445a408c131e38cab1c235aa6d2bb7a0bb20d", "reference": "48f445a408c131e38cab1c235aa6d2bb7a0bb20d", "shasum": ""}, "require": {"php": "^7.4 || ^8.0", "phpdocumentor/reflection-common": "^2.0"}, "require-dev": {"ext-tokenizer": "*", "phpstan/extension-installer": "^1.1", "phpstan/phpstan": "^1.8", "phpstan/phpstan-phpunit": "^1.1", "phpunit/phpunit": "^9.5", "rector/rector": "^0.13.9", "vimeo/psalm": "^4.25"}, "type": "library", "extra": {"branch-alias": {"dev-1.x": "1.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A PSR-5 based resolver of Class names, Types and Structural Element Names", "support": {"issues": "https://github.com/phpDocumentor/TypeResolver/issues", "source": "https://github.com/phpDocumentor/TypeResolver/tree/1.6.2"}, "time": "2022-10-14T12:47:21+00:00"}, {"name": "phpstan/phpdoc-parser", "version": "1.5.1", "source": {"type": "git", "url": "https://github.com/phpstan/phpdoc-parser.git", "reference": "981cc368a216c988e862a75e526b6076987d1b50"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/981cc368a216c988e862a75e526b6076987d1b50", "reference": "981cc368a216c988e862a75e526b6076987d1b50", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"php-parallel-lint/php-parallel-lint": "^1.2", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^1.5", "phpstan/phpstan-strict-rules": "^1.0", "phpunit/phpunit": "^9.5", "symfony/process": "^5.2"}, "type": "library", "autoload": {"psr-4": {"PHPStan\\PhpDocParser\\": ["src/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "PHPDoc parser with support for nullable, intersection and generic types", "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.5.1"}, "time": "2022-05-05T11:32:40+00:00"}, {"name": "phpunit/php-code-coverage", "version": "10.1.7", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "reference": "355324ca4980b8916c18b9db29f3ef484078f26e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/355324ca4980b8916c18b9db29f3ef484078f26e", "reference": "355324ca4980b8916c18b9db29f3ef484078f26e", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "ext-xmlwriter": "*", "nikic/php-parser": "^4.15", "php": ">=8.1", "phpunit/php-file-iterator": "^4.0", "phpunit/php-text-template": "^3.0", "sebastian/code-unit-reverse-lookup": "^3.0", "sebastian/complexity": "^3.0", "sebastian/environment": "^6.0", "sebastian/lines-of-code": "^2.0", "sebastian/version": "^4.0", "theseer/tokenizer": "^1.2.0"}, "require-dev": {"phpunit/phpunit": "^10.1"}, "suggest": {"ext-pcov": "PHP extension that provides line coverage", "ext-xdebug": "PHP extension that provides line coverage as well as branch and path coverage"}, "type": "library", "extra": {"branch-alias": {"dev-main": "10.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that provides collection, processing, and rendering functionality for PHP code coverage information.", "homepage": "https://github.com/sebastian<PERSON>mann/php-code-coverage", "keywords": ["coverage", "testing", "xunit"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/10.1.7"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-10-04T15:34:17+00:00"}, {"name": "phpunit/php-file-iterator", "version": "4.1.0", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "reference": "a95037b6d9e608ba092da1b23931e537cadc3c3c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/a95037b6d9e608ba092da1b23931e537cadc3c3c", "reference": "a95037b6d9e608ba092da1b23931e537cadc3c3c", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "FilterIterator implementation that filters files based on a list of suffixes.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/", "keywords": ["filesystem", "iterator"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/tree/4.1.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-08-31T06:24:48+00:00"}, {"name": "phpunit/php-invoker", "version": "4.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-invoker.git", "reference": "f5e568ba02fa5ba0ddd0f618391d5a9ea50b06d7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-invoker/zipball/f5e568ba02fa5ba0ddd0f618391d5a9ea50b06d7", "reference": "f5e568ba02fa5ba0ddd0f618391d5a9ea50b06d7", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"ext-pcntl": "*", "phpunit/phpunit": "^10.0"}, "suggest": {"ext-pcntl": "*"}, "type": "library", "extra": {"branch-alias": {"dev-main": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Invoke callables with a timeout", "homepage": "https://github.com/sebastian<PERSON>mann/php-invoker/", "keywords": ["process"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-invoker/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-invoker/tree/4.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T06:56:09+00:00"}, {"name": "phpunit/php-text-template", "version": "3.0.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template.git", "reference": "0c7b06ff49e3d5072f057eb1fa59258bf287a748"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-text-template/zipball/0c7b06ff49e3d5072f057eb1fa59258bf287a748", "reference": "0c7b06ff49e3d5072f057eb1fa59258bf287a748", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Simple template engine.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/", "keywords": ["template"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/tree/3.0.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-08-31T14:07:24+00:00"}, {"name": "phpunit/php-timer", "version": "6.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-timer.git", "reference": "e2a2d67966e740530f4a3343fe2e030ffdc1161d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-timer/zipball/e2a2d67966e740530f4a3343fe2e030ffdc1161d", "reference": "e2a2d67966e740530f4a3343fe2e030ffdc1161d", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "6.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Utility class for timing", "homepage": "https://github.com/sebastian<PERSON>mann/php-timer/", "keywords": ["timer"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-timer/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-timer/tree/6.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T06:57:52+00:00"}, {"name": "phpunit/phpunit", "version": "10.4.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/phpunit.git", "reference": "62bd7af13d282deeb95650077d28ba3600ca321c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/phpunit/zipball/62bd7af13d282deeb95650077d28ba3600ca321c", "reference": "62bd7af13d282deeb95650077d28ba3600ca321c", "shasum": ""}, "require": {"ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-xml": "*", "ext-xmlwriter": "*", "myclabs/deep-copy": "^1.10.1", "phar-io/manifest": "^2.0.3", "phar-io/version": "^3.0.2", "php": ">=8.1", "phpunit/php-code-coverage": "^10.1.5", "phpunit/php-file-iterator": "^4.0", "phpunit/php-invoker": "^4.0", "phpunit/php-text-template": "^3.0", "phpunit/php-timer": "^6.0", "sebastian/cli-parser": "^2.0", "sebastian/code-unit": "^2.0", "sebastian/comparator": "^5.0", "sebastian/diff": "^5.0", "sebastian/environment": "^6.0", "sebastian/exporter": "^5.1", "sebastian/global-state": "^6.0.1", "sebastian/object-enumerator": "^5.0", "sebastian/recursion-context": "^5.0", "sebastian/type": "^4.0", "sebastian/version": "^4.0"}, "suggest": {"ext-soap": "To be able to generate mocks based on WSDL files"}, "bin": ["phpunit"], "type": "library", "extra": {"branch-alias": {"dev-main": "10.4-dev"}}, "autoload": {"files": ["src/Framework/Assert/Functions.php"], "classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "The PHP Unit Testing framework.", "homepage": "https://phpunit.de/", "keywords": ["phpunit", "testing", "xunit"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/phpunit/issues", "security": "https://github.com/sebastian<PERSON>mann/phpunit/security/policy", "source": "https://github.com/sebastian<PERSON>mann/phpunit/tree/10.4.1"}, "funding": [{"url": "https://phpunit.de/sponsors.html", "type": "custom"}, {"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/phpunit/phpunit", "type": "tidelift"}], "time": "2023-10-08T05:01:11+00:00"}, {"name": "psalm/plugin-phpunit", "version": "0.18.4", "source": {"type": "git", "url": "https://github.com/psalm/psalm-plugin-phpunit.git", "reference": "e4ab3096653d9eb6f6d0ea5f4461898d59ae4dbc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/psalm/psalm-plugin-phpunit/zipball/e4ab3096653d9eb6f6d0ea5f4461898d59ae4dbc", "reference": "e4ab3096653d9eb6f6d0ea5f4461898d59ae4dbc", "shasum": ""}, "require": {"composer/package-versions-deprecated": "^1.10", "composer/semver": "^1.4 || ^2.0 || ^3.0", "ext-simplexml": "*", "php": "^7.1 || ^8.0", "vimeo/psalm": "dev-master || dev-4.x || ^4.7.1 || ^5@beta || ^5.0"}, "conflict": {"phpunit/phpunit": "<7.5"}, "require-dev": {"codeception/codeception": "^4.0.3", "php": "^7.3 || ^8.0", "phpunit/phpunit": "^7.5 || ^8.0 || ^9.0", "squizlabs/php_codesniffer": "^3.3.1", "weirdan/codeception-psalm-module": "^0.11.0", "weirdan/prophecy-shim": "^1.0 || ^2.0"}, "type": "psalm-plugin", "extra": {"psalm": {"pluginClass": "Psalm\\PhpUnitPlugin\\Plugin"}}, "autoload": {"psr-4": {"Psalm\\PhpUnitPlugin\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Psalm plugin for PHPUnit", "support": {"issues": "https://github.com/psalm/psalm-plugin-phpunit/issues", "source": "https://github.com/psalm/psalm-plugin-phpunit/tree/0.18.4"}, "time": "2022-12-03T07:47:07+00:00"}, {"name": "psr/cache", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/cache.git", "reference": "aa5030cfa5405eccfdcb1083ce040c2cb8d253bf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/cache/zipball/aa5030cfa5405eccfdcb1083ce040c2cb8d253bf", "reference": "aa5030cfa5405eccfdcb1083ce040c2cb8d253bf", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "support": {"source": "https://github.com/php-fig/cache/tree/3.0.0"}, "time": "2021-02-03T23:26:27+00:00"}, {"name": "psr/log", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "fe5ea303b0887d5caefd3d431c3e61ad47037001"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/fe5ea303b0887d5caefd3d431c3e61ad47037001", "reference": "fe5ea303b0887d5caefd3d431c3e61ad47037001", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/3.0.0"}, "time": "2021-07-14T16:46:02+00:00"}, {"name": "sebastian/cli-parser", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/cli-parser.git", "reference": "efdc130dbbbb8ef0b545a994fd811725c5282cae"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/cli-parser/zipball/efdc130dbbbb8ef0b545a994fd811725c5282cae", "reference": "efdc130dbbbb8ef0b545a994fd811725c5282cae", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for parsing CLI options", "homepage": "https://github.com/sebastian<PERSON>mann/cli-parser", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/tree/2.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T06:58:15+00:00"}, {"name": "sebastian/code-unit", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/code-unit.git", "reference": "a81fee9eef0b7a76af11d121767abc44c104e503"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/code-unit/zipball/a81fee9eef0b7a76af11d121767abc44c104e503", "reference": "a81fee9eef0b7a76af11d121767abc44c104e503", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Collection of value objects that represent the PHP code units", "homepage": "https://github.com/sebastian<PERSON>mann/code-unit", "support": {"issues": "https://github.com/sebastian<PERSON>mann/code-unit/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/code-unit/tree/2.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T06:58:43+00:00"}, {"name": "sebastian/code-unit-reverse-lookup", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/code-unit-reverse-lookup.git", "reference": "5e3a687f7d8ae33fb362c5c0743794bbb2420a1d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/code-unit-reverse-lookup/zipball/5e3a687f7d8ae33fb362c5c0743794bbb2420a1d", "reference": "5e3a687f7d8ae33fb362c5c0743794bbb2420a1d", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Looks up which function or method a line of code belongs to", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/tree/3.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T06:59:15+00:00"}, {"name": "sebastian/comparator", "version": "5.0.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/comparator.git", "reference": "2db5010a484d53ebf536087a70b4a5423c102372"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/2db5010a484d53ebf536087a70b4a5423c102372", "reference": "2db5010a484d53ebf536087a70b4a5423c102372", "shasum": ""}, "require": {"ext-dom": "*", "ext-mbstring": "*", "php": ">=8.1", "sebastian/diff": "^5.0", "sebastian/exporter": "^5.0"}, "require-dev": {"phpunit/phpunit": "^10.3"}, "type": "library", "extra": {"branch-alias": {"dev-main": "5.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to compare PHP values for equality", "homepage": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator", "keywords": ["comparator", "compare", "equality"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "security": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator/security/policy", "source": "https://github.com/sebastian<PERSON>mann/comparator/tree/5.0.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-08-14T13:18:12+00:00"}, {"name": "sebastian/complexity", "version": "3.1.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/complexity.git", "reference": "68cfb347a44871f01e33ab0ef8215966432f6957"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/complexity/zipball/68cfb347a44871f01e33ab0ef8215966432f6957", "reference": "68cfb347a44871f01e33ab0ef8215966432f6957", "shasum": ""}, "require": {"nikic/php-parser": "^4.10", "php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for calculating the complexity of PHP code units", "homepage": "https://github.com/sebastian<PERSON>mann/complexity", "support": {"issues": "https://github.com/sebastian<PERSON>mann/complexity/issues", "security": "https://github.com/sebastian<PERSON>mann/complexity/security/policy", "source": "https://github.com/sebastian<PERSON>mann/complexity/tree/3.1.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-09-28T11:50:59+00:00"}, {"name": "sebastian/diff", "version": "5.0.3", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/diff.git", "reference": "912dc2fbe3e3c1e7873313cc801b100b6c68c87b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/diff/zipball/912dc2fbe3e3c1e7873313cc801b100b6c68c87b", "reference": "912dc2fbe3e3c1e7873313cc801b100b6c68c87b", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0", "symfony/process": "^4.2 || ^5"}, "type": "library", "extra": {"branch-alias": {"dev-main": "5.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Diff implementation", "homepage": "https://github.com/sebastian<PERSON>mann/diff", "keywords": ["diff", "udiff", "unidiff", "unified diff"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/diff/issues", "security": "https://github.com/sebastian<PERSON>mann/diff/security/policy", "source": "https://github.com/sebastian<PERSON>mann/diff/tree/5.0.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-05-01T07:48:21+00:00"}, {"name": "sebastian/environment", "version": "6.0.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/environment.git", "reference": "43c751b41d74f96cbbd4e07b7aec9675651e2951"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/environment/zipball/43c751b41d74f96cbbd4e07b7aec9675651e2951", "reference": "43c751b41d74f96cbbd4e07b7aec9675651e2951", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "suggest": {"ext-posix": "*"}, "type": "library", "extra": {"branch-alias": {"dev-main": "6.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to handle HHVM/PHP environments", "homepage": "https://github.com/sebastian<PERSON>mann/environment", "keywords": ["Xdebug", "environment", "hhvm"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/environment/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/environment/security/policy", "source": "https://github.com/sebastian<PERSON>mann/environment/tree/6.0.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-04-11T05:39:26+00:00"}, {"name": "sebastian/exporter", "version": "5.1.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/exporter.git", "reference": "64f51654862e0f5e318db7e9dcc2292c63cdbddc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/exporter/zipball/64f51654862e0f5e318db7e9dcc2292c63cdbddc", "reference": "64f51654862e0f5e318db7e9dcc2292c63cdbddc", "shasum": ""}, "require": {"ext-mbstring": "*", "php": ">=8.1", "sebastian/recursion-context": "^5.0"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "5.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Provides the functionality to export PHP variables for visualization", "homepage": "https://www.github.com/sebastianbergmann/exporter", "keywords": ["export", "exporter"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/exporter/issues", "security": "https://github.com/sebastian<PERSON>mann/exporter/security/policy", "source": "https://github.com/sebastian<PERSON>mann/exporter/tree/5.1.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-09-24T13:22:09+00:00"}, {"name": "sebastian/global-state", "version": "6.0.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/global-state.git", "reference": "7ea9ead78f6d380d2a667864c132c2f7b83055e4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/global-state/zipball/7ea9ead78f6d380d2a667864c132c2f7b83055e4", "reference": "7ea9ead78f6d380d2a667864c132c2f7b83055e4", "shasum": ""}, "require": {"php": ">=8.1", "sebastian/object-reflector": "^3.0", "sebastian/recursion-context": "^5.0"}, "require-dev": {"ext-dom": "*", "phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "6.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Snapshotting of global state", "homepage": "http://www.github.com/sebastian<PERSON>mann/global-state", "keywords": ["global state"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/global-state/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/global-state/security/policy", "source": "https://github.com/sebastian<PERSON>mann/global-state/tree/6.0.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-07-19T07:19:23+00:00"}, {"name": "sebastian/lines-of-code", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code.git", "reference": "649e40d279e243d985aa8fb6e74dd5bb28dc185d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/lines-of-code/zipball/649e40d279e243d985aa8fb6e74dd5bb28dc185d", "reference": "649e40d279e243d985aa8fb6e74dd5bb28dc185d", "shasum": ""}, "require": {"nikic/php-parser": "^4.10", "php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for counting the lines of code in PHP source code", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/tree/2.0.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-08-31T09:25:50+00:00"}, {"name": "sebastian/object-enumerator", "version": "5.0.0", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/object-enumerator.git", "reference": "202d0e344a580d7f7d04b3fafce6933e59dae906"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-enumerator/zipball/202d0e344a580d7f7d04b3fafce6933e59dae906", "reference": "202d0e344a580d7f7d04b3fafce6933e59dae906", "shasum": ""}, "require": {"php": ">=8.1", "sebastian/object-reflector": "^3.0", "sebastian/recursion-context": "^5.0"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "5.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Traverses array structures and object graphs to enumerate all referenced objects", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/tree/5.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T07:08:32+00:00"}, {"name": "sebastian/object-reflector", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector.git", "reference": "24ed13d98130f0e7122df55d06c5c4942a577957"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/object-reflector/zipball/24ed13d98130f0e7122df55d06c5c4942a577957", "reference": "24ed13d98130f0e7122df55d06c5c4942a577957", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Allows reflection of object attributes, including inherited and non-public ones", "homepage": "https://github.com/sebastian<PERSON>mann/object-reflector/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/tree/3.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T07:06:18+00:00"}, {"name": "sebastian/recursion-context", "version": "5.0.0", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "reference": "05909fb5bc7df4c52992396d0116aed689f93712"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/05909fb5bc7df4c52992396d0116aed689f93712", "reference": "05909fb5bc7df4c52992396d0116aed689f93712", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "5.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to recursively process PHP variables", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/tree/5.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T07:05:40+00:00"}, {"name": "sebastian/type", "version": "4.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/type.git", "reference": "462699a16464c3944eefc02ebdd77882bd3925bf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/type/zipball/462699a16464c3944eefc02ebdd77882bd3925bf", "reference": "462699a16464c3944eefc02ebdd77882bd3925bf", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Collection of value objects that represent the types of the PHP type system", "homepage": "https://github.com/sebastian<PERSON>mann/type", "support": {"issues": "https://github.com/sebastian<PERSON>mann/type/issues", "source": "https://github.com/sebastian<PERSON>mann/type/tree/4.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T07:10:45+00:00"}, {"name": "sebastian/version", "version": "4.0.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/version.git", "reference": "c51fa83a5d8f43f1402e3f32a005e6262244ef17"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/version/zipball/c51fa83a5d8f43f1402e3f32a005e6262244ef17", "reference": "c51fa83a5d8f43f1402e3f32a005e6262244ef17", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that helps with managing the version number of Git-hosted PHP projects", "homepage": "https://github.com/sebastian<PERSON>mann/version", "support": {"issues": "https://github.com/sebastian<PERSON>mann/version/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/version/tree/4.0.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-07T11:34:05+00:00"}, {"name": "seld/jsonlint", "version": "1.9.0", "source": {"type": "git", "url": "https://github.com/Seldaek/jsonlint.git", "reference": "4211420d25eba80712bff236a98960ef68b866b7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/jsonlint/zipball/4211420d25eba80712bff236a98960ef68b866b7", "reference": "4211420d25eba80712bff236a98960ef68b866b7", "shasum": ""}, "require": {"php": "^5.3 || ^7.0 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.5", "phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.0 || ^8.5.13"}, "bin": ["bin/jsonlint"], "type": "library", "autoload": {"psr-4": {"Seld\\JsonLint\\": "src/Seld/JsonLint/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "JSON Linter", "keywords": ["json", "linter", "parser", "validator"], "support": {"issues": "https://github.com/Seldaek/jsonlint/issues", "source": "https://github.com/Seldaek/jsonlint/tree/1.9.0"}, "funding": [{"url": "https://github.com/Seldaek", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/seld/jsonlint", "type": "tidelift"}], "time": "2022-04-01T13:37:23+00:00"}, {"name": "slevomat/coding-standard", "version": "7.2.1", "source": {"type": "git", "url": "https://github.com/slevomat/coding-standard.git", "reference": "aff06ae7a84e4534bf6f821dc982a93a5d477c90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/slevomat/coding-standard/zipball/aff06ae7a84e4534bf6f821dc982a93a5d477c90", "reference": "aff06ae7a84e4534bf6f821dc982a93a5d477c90", "shasum": ""}, "require": {"dealerdirect/phpcodesniffer-composer-installer": "^0.6.2 || ^0.7", "php": "^7.2 || ^8.0", "phpstan/phpdoc-parser": "^1.5.1", "squizlabs/php_codesniffer": "^3.6.2"}, "require-dev": {"phing/phing": "2.17.3", "php-parallel-lint/php-parallel-lint": "1.3.2", "phpstan/phpstan": "1.4.10|1.7.1", "phpstan/phpstan-deprecation-rules": "1.0.0", "phpstan/phpstan-phpunit": "1.0.0|1.1.1", "phpstan/phpstan-strict-rules": "1.2.3", "phpunit/phpunit": "7.5.20|8.5.21|9.5.20"}, "type": "phpcodesniffer-standard", "extra": {"branch-alias": {"dev-master": "7.x-dev"}}, "autoload": {"psr-4": {"SlevomatCodingStandard\\": "SlevomatCodingStandard"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Slevomat Coding Standard for PHP_CodeSniffer complements Consistence Coding Standard by providing sniffs with additional checks.", "support": {"issues": "https://github.com/slevomat/coding-standard/issues", "source": "https://github.com/slevomat/coding-standard/tree/7.2.1"}, "funding": [{"url": "https://github.com/kukulich", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/slevomat/coding-standard", "type": "tidelift"}], "time": "2022-05-25T10:58:12+00:00"}, {"name": "spatie/array-to-xml", "version": "3.1.5", "source": {"type": "git", "url": "https://github.com/spatie/array-to-xml.git", "reference": "13f76acef5362d15c71ae1ac6350cc3df5e25e43"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spatie/array-to-xml/zipball/13f76acef5362d15c71ae1ac6350cc3df5e25e43", "reference": "13f76acef5362d15c71ae1ac6350cc3df5e25e43", "shasum": ""}, "require": {"ext-dom": "*", "php": "^8.0"}, "require-dev": {"mockery/mockery": "^1.2", "pestphp/pest": "^1.21", "spatie/pest-plugin-snapshots": "^1.1"}, "type": "library", "autoload": {"psr-4": {"Spatie\\ArrayToXml\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://freek.dev", "role": "Developer"}], "description": "Convert an array to xml", "homepage": "https://github.com/spatie/array-to-xml", "keywords": ["array", "convert", "xml"], "support": {"source": "https://github.com/spatie/array-to-xml/tree/3.1.5"}, "funding": [{"url": "https://spatie.be/open-source/support-us", "type": "custom"}, {"url": "https://github.com/spatie", "type": "github"}], "time": "2022-12-24T13:43:51+00:00"}, {"name": "squizlabs/php_codesniffer", "version": "3.7.2", "source": {"type": "git", "url": "https://github.com/squizlabs/PHP_CodeSniffer.git", "reference": "ed8e00df0a83aa96acf703f8c2979ff33341f879"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/squizlabs/PHP_CodeSniffer/zipball/ed8e00df0a83aa96acf703f8c2979ff33341f879", "reference": "ed8e00df0a83aa96acf703f8c2979ff33341f879", "shasum": ""}, "require": {"ext-simplexml": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*", "php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "^4.0 || ^5.0 || ^6.0 || ^7.0"}, "bin": ["bin/phpcs", "bin/phpcbf"], "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "role": "lead"}], "description": "PHP_CodeSniffer tokenizes PHP, JavaScript and CSS files and detects violations of a defined set of coding standards.", "homepage": "https://github.com/squizlabs/PHP_CodeSniffer", "keywords": ["phpcs", "standards", "static analysis"], "support": {"issues": "https://github.com/squizlabs/PHP_CodeSniffer/issues", "source": "https://github.com/squizlabs/PHP_CodeSniffer", "wiki": "https://github.com/squizlabs/PHP_CodeSniffer/wiki"}, "time": "2023-02-22T23:07:41+00:00"}, {"name": "symfony/console", "version": "v6.2.8", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "3582d68a64a86ec25240aaa521ec8bc2342b369b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/3582d68a64a86ec25240aaa521ec8bc2342b369b", "reference": "3582d68a64a86ec25240aaa521ec8bc2342b369b", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-mbstring": "~1.0", "symfony/service-contracts": "^1.1|^2|^3", "symfony/string": "^5.4|^6.0"}, "conflict": {"symfony/dependency-injection": "<5.4", "symfony/dotenv": "<5.4", "symfony/event-dispatcher": "<5.4", "symfony/lock": "<5.4", "symfony/process": "<5.4"}, "provide": {"psr/log-implementation": "1.0|2.0|3.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^5.4|^6.0", "symfony/dependency-injection": "^5.4|^6.0", "symfony/event-dispatcher": "^5.4|^6.0", "symfony/lock": "^5.4|^6.0", "symfony/process": "^5.4|^6.0", "symfony/var-dumper": "^5.4|^6.0"}, "suggest": {"psr/log": "For using the console logger", "symfony/event-dispatcher": "", "symfony/lock": "", "symfony/process": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Eases the creation of beautiful and testable command line interfaces", "homepage": "https://symfony.com", "keywords": ["cli", "command-line", "console", "terminal"], "support": {"source": "https://github.com/symfony/console/tree/v6.2.8"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-03-29T21:42:15+00:00"}, {"name": "symfony/deprecation-contracts", "version": "v3.3.0", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "7c3aff79d10325257a001fcf92d991f24fc967cf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/7c3aff79d10325257a001fcf92d991f24fc967cf", "reference": "7c3aff79d10325257a001fcf92d991f24fc967cf", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.4-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v3.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-05-23T14:45:45+00:00"}, {"name": "symfony/filesystem", "version": "v6.2.7", "source": {"type": "git", "url": "https://github.com/symfony/filesystem.git", "reference": "82b6c62b959f642d000456f08c6d219d749215b3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/filesystem/zipball/82b6c62b959f642d000456f08c6d219d749215b3", "reference": "82b6c62b959f642d000456f08c6d219d749215b3", "shasum": ""}, "require": {"php": ">=8.1", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.8"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Filesystem\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides basic utilities for the filesystem", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/filesystem/tree/v6.2.7"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-02-14T08:44:56+00:00"}, {"name": "symfony/finder", "version": "v6.2.7", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "20808dc6631aecafbe67c186af5dcb370be3a0eb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/20808dc6631aecafbe67c186af5dcb370be3a0eb", "reference": "20808dc6631aecafbe67c186af5dcb370be3a0eb", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"symfony/filesystem": "^6.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Finds files and directories via an intuitive fluent interface", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/finder/tree/v6.2.7"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-02-16T09:57:23+00:00"}, {"name": "symfony/options-resolver", "version": "v6.2.7", "source": {"type": "git", "url": "https://github.com/symfony/options-resolver.git", "reference": "aa0e85b53bbb2b4951960efd61d295907eacd629"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/options-resolver/zipball/aa0e85b53bbb2b4951960efd61d295907eacd629", "reference": "aa0e85b53bbb2b4951960efd61d295907eacd629", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.1|^3"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\OptionsResolver\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an improved replacement for the array_replace PHP function", "homepage": "https://symfony.com", "keywords": ["config", "configuration", "options"], "support": {"source": "https://github.com/symfony/options-resolver/tree/v6.2.7"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-02-14T08:44:56+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.28.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "ea208ce43cbb04af6867b4fdddb1bdbf84cc28cb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/ea208ce43cbb04af6867b4fdddb1bdbf84cc28cb", "reference": "ea208ce43cbb04af6867b4fdddb1bdbf84cc28cb", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-ctype": "*"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.28-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.28.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-01-26T09:26:14+00:00"}, {"name": "symfony/polyfill-intl-grapheme", "version": "v1.27.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-grapheme.git", "reference": "511a08c03c1960e08a883f4cffcacd219b758354"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/511a08c03c1960e08a883f4cffcacd219b758354", "reference": "511a08c03c1960e08a883f4cffcacd219b758354", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.27-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Grapheme\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's grapheme_* functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "grapheme", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/v1.27.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-11-03T14:55:06+00:00"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.27.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-normalizer.git", "reference": "19bd1e4fcd5b91116f14d8533c57831ed00571b6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/19bd1e4fcd5b91116f14d8533c57831ed00571b6", "reference": "19bd1e4fcd5b91116f14d8533c57831ed00571b6", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.27-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.27.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-11-03T14:55:06+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.27.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "8ad114f6b39e2c98a8b0e3bd907732c207c2b534"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/8ad114f6b39e2c98a8b0e3bd907732c207c2b534", "reference": "8ad114f6b39e2c98a8b0e3bd907732c207c2b534", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.27-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.27.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-11-03T14:55:06+00:00"}, {"name": "symfony/process", "version": "v6.2.8", "source": {"type": "git", "url": "https://github.com/symfony/process.git", "reference": "75ed64103df4f6615e15a7fe38b8111099f47416"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/process/zipball/75ed64103df4f6615e15a7fe38b8111099f47416", "reference": "75ed64103df4f6615e15a7fe38b8111099f47416", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Process\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Executes commands in sub-processes", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/process/tree/v6.2.8"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-03-09T16:20:02+00:00"}, {"name": "symfony/service-contracts", "version": "v2.5.2", "source": {"type": "git", "url": "https://github.com/symfony/service-contracts.git", "reference": "4b426aac47d6427cc1a1d0f7e2ac724627f5966c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/service-contracts/zipball/4b426aac47d6427cc1a1d0f7e2ac724627f5966c", "reference": "4b426aac47d6427cc1a1d0f7e2ac724627f5966c", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/container": "^1.1", "symfony/deprecation-contracts": "^2.1|^3"}, "conflict": {"ext-psr": "<1.1|>=2"}, "suggest": {"symfony/service-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/service-contracts/tree/v2.5.2"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-05-30T19:17:29+00:00"}, {"name": "symfony/string", "version": "v6.2.8", "source": {"type": "git", "url": "https://github.com/symfony/string.git", "reference": "193e83bbd6617d6b2151c37fff10fa7168ebddef"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/string/zipball/193e83bbd6617d6b2151c37fff10fa7168ebddef", "reference": "193e83bbd6617d6b2151c37fff10fa7168ebddef", "shasum": ""}, "require": {"php": ">=8.1", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-intl-grapheme": "~1.0", "symfony/polyfill-intl-normalizer": "~1.0", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/translation-contracts": "<2.0"}, "require-dev": {"symfony/error-handler": "^5.4|^6.0", "symfony/http-client": "^5.4|^6.0", "symfony/intl": "^6.2", "symfony/translation-contracts": "^2.0|^3.0", "symfony/var-exporter": "^5.4|^6.0"}, "type": "library", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\String\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an object-oriented API to strings and deals with bytes, UTF-8 code points and grapheme clusters in a unified way", "homepage": "https://symfony.com", "keywords": ["grapheme", "i18n", "string", "unicode", "utf-8", "utf8"], "support": {"source": "https://github.com/symfony/string/tree/v6.2.8"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-03-20T16:06:02+00:00"}, {"name": "theseer/tokenizer", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/theseer/tokenizer.git", "reference": "34a41e998c2183e22995f158c581e7b5e755ab9e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/theseer/tokenizer/zipball/34a41e998c2183e22995f158c581e7b5e755ab9e", "reference": "34a41e998c2183e22995f158c581e7b5e755ab9e", "shasum": ""}, "require": {"ext-dom": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*", "php": "^7.2 || ^8.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A small library for converting tokenized PHP source code into XML and potentially other formats", "support": {"issues": "https://github.com/theseer/tokenizer/issues", "source": "https://github.com/theseer/tokenizer/tree/1.2.1"}, "funding": [{"url": "https://github.com/theseer", "type": "github"}], "time": "2021-07-28T10:34:58+00:00"}, {"name": "vimeo/psalm", "version": "5.9.0", "source": {"type": "git", "url": "https://github.com/vimeo/psalm.git", "reference": "8b9ad1eb9e8b7d3101f949291da2b9f7767cd163"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/vimeo/psalm/zipball/8b9ad1eb9e8b7d3101f949291da2b9f7767cd163", "reference": "8b9ad1eb9e8b7d3101f949291da2b9f7767cd163", "shasum": ""}, "require": {"amphp/amp": "^2.4.2", "amphp/byte-stream": "^1.5", "composer-runtime-api": "^2", "composer/semver": "^1.4 || ^2.0 || ^3.0", "composer/xdebug-handler": "^2.0 || ^3.0", "dnoegel/php-xdg-base-dir": "^0.1.1", "ext-ctype": "*", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-simplexml": "*", "ext-tokenizer": "*", "felixfbecker/advanced-json-rpc": "^3.1", "felixfbecker/language-server-protocol": "^1.5.2", "fidry/cpu-core-counter": "^0.4.1 || ^0.5.1", "netresearch/jsonmapper": "^1.0 || ^2.0 || ^3.0 || ^4.0", "nikic/php-parser": "^4.14", "php": "^7.4 || ~8.0.0 || ~8.1.0 || ~8.2.0", "sebastian/diff": "^4.0 || ^5.0", "spatie/array-to-xml": "^2.17.0 || ^3.0", "symfony/console": "^4.1.6 || ^5.0 || ^6.0", "symfony/filesystem": "^5.4 || ^6.0"}, "provide": {"psalm/psalm": "self.version"}, "require-dev": {"amphp/phpunit-util": "^2.0", "bamarni/composer-bin-plugin": "^1.4", "brianium/paratest": "^6.9", "ext-curl": "*", "mockery/mockery": "^1.5", "nunomaduro/mock-final-classes": "^1.1", "php-parallel-lint/php-parallel-lint": "^1.2", "phpstan/phpdoc-parser": "^1.6", "phpunit/phpunit": "^9.6", "psalm/plugin-mockery": "^1.1", "psalm/plugin-phpunit": "^0.18", "slevomat/coding-standard": "^8.4", "squizlabs/php_codesniffer": "^3.6", "symfony/process": "^4.4 || ^5.0 || ^6.0"}, "suggest": {"ext-curl": "In order to send data to shepherd", "ext-igbinary": "^2.0.5 is required, used to serialize caching data"}, "bin": ["psalm", "psalm-language-server", "psalm-plugin", "psalm-refactor", "psalter"], "type": "library", "extra": {"branch-alias": {"dev-master": "5.x-dev", "dev-4.x": "4.x-dev", "dev-3.x": "3.x-dev", "dev-2.x": "2.x-dev", "dev-1.x": "1.x-dev"}}, "autoload": {"psr-4": {"Psalm\\": "src/Psalm/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "A static analysis tool for finding errors in PHP applications", "keywords": ["code", "inspection", "php", "static analysis"], "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/5.9.0"}, "time": "2023-03-29T21:38:21+00:00"}, {"name": "webimpress/coding-standard", "version": "1.3.1", "source": {"type": "git", "url": "https://github.com/webimpress/coding-standard.git", "reference": "b26557e2386711ecb74f22718f4b4bde5ddbc899"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webimpress/coding-standard/zipball/b26557e2386711ecb74f22718f4b4bde5ddbc899", "reference": "b26557e2386711ecb74f22718f4b4bde5ddbc899", "shasum": ""}, "require": {"php": "^7.3 || ^8.0", "squizlabs/php_codesniffer": "^3.7.2"}, "require-dev": {"phpunit/phpunit": "^9.6.4"}, "type": "phpcodesniffer-standard", "extra": {"dev-master": "1.2.x-dev", "dev-develop": "1.3.x-dev"}, "autoload": {"psr-4": {"WebimpressCodingStandard\\": "src/WebimpressCodingStandard/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-2-<PERSON><PERSON>"], "description": "Webimpress Coding Standard", "keywords": ["Coding Standard", "PSR-2", "phpcs", "psr-12", "webimpress"], "support": {"issues": "https://github.com/webimpress/coding-standard/issues", "source": "https://github.com/webimpress/coding-standard/tree/1.3.1"}, "funding": [{"url": "https://github.com/michalbundyra", "type": "github"}], "time": "2023-03-09T15:05:18+00:00"}, {"name": "webmozart/assert", "version": "1.11.0", "source": {"type": "git", "url": "https://github.com/webmozarts/assert.git", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozarts/assert/zipball/11cb2199493b2f8a3b53e7f19068fc6aac760991", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991", "shasum": ""}, "require": {"ext-ctype": "*", "php": "^7.2 || ^8.0"}, "conflict": {"phpstan/phpstan": "<0.12.20", "vimeo/psalm": "<4.6.1 || 4.6.2"}, "require-dev": {"phpunit/phpunit": "^8.5.13"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.10-dev"}}, "autoload": {"psr-4": {"Webmozart\\Assert\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Assertions to validate method input/output with nice error messages.", "keywords": ["assert", "check", "validate"], "support": {"issues": "https://github.com/webmozarts/assert/issues", "source": "https://github.com/webmozarts/assert/tree/1.11.0"}, "time": "2022-06-03T18:03:27+00:00"}, {"name": "webmozart/glob", "version": "4.6.0", "source": {"type": "git", "url": "https://github.com/webmozarts/glob.git", "reference": "3c17f7dec3d9d0e87b575026011f2e75a56ed655"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozarts/glob/zipball/3c17f7dec3d9d0e87b575026011f2e75a56ed655", "reference": "3c17f7dec3d9d0e87b575026011f2e75a56ed655", "shasum": ""}, "require": {"php": "^7.3 || ^8.0.0"}, "require-dev": {"phpunit/phpunit": "^9.5", "symfony/filesystem": "^5.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.1-dev"}}, "autoload": {"psr-4": {"Webmozart\\Glob\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "A PHP implementation of Ant's glob.", "support": {"issues": "https://github.com/webmozarts/glob/issues", "source": "https://github.com/webmozarts/glob/tree/4.6.0"}, "time": "2022-05-24T19:45:58+00:00"}], "aliases": [], "minimum-stability": "stable", "stability-flags": [], "prefer-stable": false, "prefer-lowest": false, "platform": {"php": "~8.1.0 || ~8.2.0 || ~8.3.0"}, "platform-dev": [], "platform-overrides": {"php": "8.1.99"}, "plugin-api-version": "2.6.0"}