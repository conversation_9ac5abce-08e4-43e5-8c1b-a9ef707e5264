#
# Default ticket filters installed for the system
#
# NOTE: The SYSTEM BAN LIST is critically important and should _never_ be
#       removed from this file. The name 'SYSTEM BAN LIST' is also important
#       and should not be translated -- it is not displayed on the interface
#
# Fields:
# isactive - (bool:0|1) true or false if the filter is initially enabled
# execorder - (int) order the filters should be executed in (lowest first)
# name - (string) Descriptive name for the filter
# notes - (string) Administrative notes (viewable internally only)
# rules - (list<FilterRule>) List of rules for the filter
#
# Fields for FilterRule:
# isactive - (bool:0|1) true if the rule should be considered
# what - (enum<email|>) field to check
# how - (enum<equals|contains|dncontain>) how to check for <val>
# val - (string) search value
#
# Fields for FilterAction
# type - type of filter action
#
---
- isactive: 1
  execorder: 99
  match_all_rules: 0
  email_id: 0
  target: Email #notrans
  name: SYSTEM BAN LIST #notrans
  notes: |
    Internal list for email banning. Do not remove
  rules:
    - isactive: 1
      w: email #notrans
      h: equal #notrans
      v: <EMAIL> #notrans
  actions:
    - sort: 1
      type: reject #notrans
