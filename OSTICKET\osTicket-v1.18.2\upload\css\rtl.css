.rtl {
    direction: rtl;
    unicode-bidi: embed;
}
.rtl .pull-left {
    float: right;
}
.rtl .pull-right {
    float: left;
}
.rtl table.list thead th a {
    background-position: 0% 50%;
    padding-right: 3px;
    padding-left: 15px;
}
.rtl table.list thead th,
.rtl table.list caption,
.rtl .dialog th,
.rtl .tip_box th {
    text-align: right;
}
.rtl .dialog h3 {
    padding-right: inherit;
    padding-left: 3em;
}
.rtl .dialog a.close {
    right: auto;
    left: 1em;
}
.rtl #nav .inactive li,
.rtl #sub_nav li {
    text-align: right;
}
.rtl #nav .inactive li a,
.rtl #sub_nav li a {
    background-position: 100% 50%;
    padding-left: 0;
    padding-right: 24px;
}
.rtl #nav li.inactive > ul {
    left: auto;
    right: -1px;
}
.rtl #sub_nav li + li > a {
    margin-left: 0;
    margin-right: 10px;
}
.rtl .tip_close {
    right: auto;
    left: 0.5em;
}
.rtl .tip_content h1 {
    padding-right: 0;
    padding-left: 1.5em;
}
.rtl #msg_notice,
.rtl #warning_bar,
.rtl #msg_warning,
.rtl #msg_error,
.rtl .error-banner {
    background-position: 99% 50%;
    background-position: calc(100% - 10px) 50%;
    padding-left: 10px;
    padding-right: 36px;
}
.rtl .form_table th, .rtl div.section-break {
    text-align: right;
}
.rtl .flush-right {
    text-align: left;
}
.rtl .flush-left {
    text-align: right;
}
.rtl .draft-saved {
    right: initial;
    left: 0.5em;
}
.rtl #sequences .manage-buttons {
    margin-right: initial;
    margin-left: 60px;
}
.rtl .row-item .button-group {
    right: initial;
    left: 0;
}
.rtl .row-item .button-group div {
    padding-left: 9px;
    padding-right: 12px;
}
.rtl .row-item .delete {
    border-left: none;
    border-right: 1px solid rgba(0,0,0,0.7);
}
.rtl [class^="icon-"].pull-left, [class*=" icon-"].pull-right {
    margin-right: 0;
    margin-left: 0.3em;
}
.rtl ul.tabs {
    padding-left: 4px;
    padding-right: 20px;
    text-align:right;
}
.rtl #response_options ul.tabs {
    padding-right:190px;
    padding-left: 4px;
}
.rtl .action-button i.icon-caret-down {
    border-left: none;
    border-right: 1px solid #aaa;
    margin-left: 0;
    margin-right: 5px;
    padding-left: 0;
    padding-right: 5px;
}
.rtl .action-dropdown ul {
    text-align: right;
}
.rtl .file {
    padding-left: initial;
    padding-right: 20px;
    margin-right: initial;
    margin-left: 20px;
    background: url(../scp/images/icons/file.gif) 100% 50% no-repeat;
}
.rtl .floating-options {
    right: auto;
    left: 0;
    padding-right: initial;
    padding-left: 5px;
}
.rtl .quicknote .header .header-right {
    right: auto;
    left: 1em;
}
.rtl .quicknote .header .options {
    border-right: 1px solid rgba(0,0,0,0.2);
    border-left: none;
    padding-right: 10px;
    padding-left: initial;
    margin-right: 5px;
    margin-left: initial;
}
.rtl i.note-type {
    border-left: 1px solid rgba(0, 0, 0, 0.2);
    border-right: none;
    padding-left: 8px;
    padding-right: initial;
}
.rtl .left-tabs {
    margin-left: auto;
    margin-right: 45px;
}
