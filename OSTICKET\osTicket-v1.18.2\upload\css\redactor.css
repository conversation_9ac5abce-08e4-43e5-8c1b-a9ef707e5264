/*
    Redactor 3

    http://imperavi.com/redactor/

    Copyright (c) 2009-2018, Imperavi LLC.
    License: http://imperavi.com/redactor/license/
*/
@keyframes fadeIn {
  from {
    opacity: 0; }
  to {
    opacity: 1; } }

@keyframes fadeOut {
  from {
    opacity: 1; }
  to {
    opacity: 0; } }

.redactor-animate-hide {
  display: none !important; }

.redactor-fadeIn {
  opacity: 0;
  animation: fadeIn .2s ease-in-out; }

.redactor-fadeOut {
  opacity: 1;
  animation: fadeOut .2s ease-in-out; }

@font-face {
  font-family: 'Redactor';
  src: url("data:application/x-font-ttf;charset=utf-8;base64,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") format("truetype");
  font-weight: normal;
  font-style: normal; }

[class^="re-icon-"], [class*=" re-icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'Redactor' !important;
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale; }

.re-icon-aligncenter:before {
  content: "\e900"; }

.re-icon-alignment:before,
.re-icon-alignleft:before {
  content: "\e901"; }

.re-icon-alignright:before {
  content: "\e902"; }

.re-icon-bold:before {
  content: "\e903"; }

.re-icon-bookmark:before {
  content: "\e904"; }

.re-icon-clips:before {
  content: "\e905"; }

.re-icon-codesnippets:before {
  content: "\e906"; }

.re-icon-deleted:before {
  content: "\e907"; }

.re-icon-expand:before {
  content: "\e908"; }

.re-icon-file:before {
  content: "\e909"; }

.re-icon-fontcolor:before {
  content: "\e90a"; }

.re-icon-fontfamily:before {
  content: "\e90b"; }

.re-icon-fontsize:before {
  content: "\e90c"; }

.re-icon-format:before {
  content: "\e90d"; }

.re-icon-html:before {
  content: "\e90e"; }

.re-icon-imagecenter:before {
  content: "\e90f"; }

.re-icon-imageleft:before {
  content: "\e910"; }

.re-icon-imageright:before {
  content: "\e911"; }

.re-icon-image:before {
  content: "\e912"; }

.re-icon-indent:before {
  content: "\e913"; }

.re-icon-inline:before {
  content: "\e914"; }

.re-icon-italic:before {
  content: "\e915"; }

.re-icon-line:before,
.re-icon-horizontalrule:before {
  content: "\e916"; }

.re-icon-link:before {
  content: "\e917"; }

.re-icon-ol:before,
.re-icon-ordered:before {
  content: "\e918"; }

.re-icon-outdent:before {
  content: "\e919"; }

.re-icon-properties:before {
  content: "\e91a"; }

.re-icon-readmore:before {
  content: "\e91b"; }

.re-icon-redo:before {
  content: "\e91c"; }

.re-icon-retract:before {
  content: "\e91d"; }

.re-icon-specialcharacters:before {
  content: "\e91e"; }

.re-icon-sub:before {
  content: "\e91f"; }

.re-icon-sup:before {
  content: "\e920"; }

.re-icon-table:before {
  content: "\e921"; }

.re-icon-textdirection:before {
  content: "\e922"; }

.re-icon-toggle:before {
  content: "\e923"; }

.re-icon-underline:before {
  content: "\e924"; }

.re-icon-undo:before {
  content: "\e925"; }

.re-icon-ul:before,
.re-icon-lists:before,
.re-icon-unordered:before {
  content: "\e926"; }

.re-icon-variable:before {
  content: "\e927"; }

.re-icon-video:before {
  content: "\e928"; }

.re-icon-widget:before {
  content: "\e929"; }

.redactor-box,
.redactor-box textarea {
  z-index: auto; }

.redactor-box {
  position: relative;
  box-sizing: border-box; }
  .redactor-box.redactor-styles-on {
    margin: 0;
    padding: 0;
    background: #fff;
    border: 1px solid rgba(0, 0, 0, 0.075);
    border-radius: 3px;
    box-shadow: none; }
  .redactor-box.redactor-inline {
    position: static; }

.redactor-focus.redactor-styles-on,
.redactor-focus:focus.redactor-styles-on {
  border-color: #82b7ec !important; }

.redactor-over:hover.redactor-styles-on {
  border-color: #ee698a !important; }

.redactor-source-view,
.redactor-source-view.redactor-styles-on {
  border-color: #000 !important; }

.redactor-in {
  position: relative;
  overflow: auto;
  white-space: normal;
  box-sizing: border-box; }
  .redactor-in:focus {
    outline: none; }

.redactor-inline .redactor-in {
  overflow: hidden; }

.redactor-in *,
.redactor-read-only * {
  outline: none !important; }

.redactor-in h1:empty,
.redactor-in h2:empty,
.redactor-in h3:empty,
.redactor-in h4:empty,
.redactor-in h5:empty,
.redactor-in h6:empty,
.redactor-in p:empty,
.redactor-in blockquote:empty,
.redactor-in div[data-redactor-tag=br]:empty,
.redactor-in div[data-redactor-tag=tbr]:empty {
  min-height: 1.5em; }

.redactor-in div[data-redactor-tag=br],
.redactor-in div[data-redactor-tag=tbr] {
  margin-top: 0;
  margin-bottom: 0; }

.redactor-in strong:empty, .redactor-in b:empty, .redactor-in em:empty, .redactor-in i:empty, .redactor-in span:empty, .redactor-in sup:empty, .redactor-in sub:empty, .redactor-in u:empty, .redactor-in ins:empty {
  display: inline-block;
  min-width: 1px;
  min-height: 1rem; }

.redactor-in table {
  empty-cells: show; }

.redactor-in li figure {
  width: auto;
  display: inline-block;
  margin: 0;
  vertical-align: top; }

.redactor-in figcaption:focus,
.redactor-in figure code:focus,
.redactor-in figure pre:focus,
.redactor-in table td:focus,
.redactor-in table th:focus {
  outline: none; }

.redactor-in figure[data-redactor-type=line] {
  margin-top: 1em;
  padding: 6px 0;
  vertical-align: baseline; }
  .redactor-in figure[data-redactor-type=line] hr {
    margin: 0;
    height: 3px;
    border: none;
    background: rgba(0, 0, 0, 0.1); }

.redactor-script-tag {
  display: none !important; }

.redactor-component {
  position: relative; }

.redactor-component[data-redactor-type="widget"]:before,
.redactor-component[data-redactor-type="video"]:before {
  width: 100%;
  height: 100%;
  content: "";
  display: block;
  position: absolute;
  z-index: 1; }

.redactor-component[data-redactor-type=image],
.redactor-component[data-redactor-type=widget] {
  clear: both; }

.redactor-component[data-redactor-type=variable] {
  white-space: nowrap;
  background: rgba(0, 125, 255, 0.75);
  color: #fff;
  display: inline-block;
  padding: 3px 6px;
  line-height: 1;
  border-radius: 4px;
  cursor: pointer; }

.redactor-component-active {
  outline: 5px solid rgba(0, 125, 255, 0.5) !important; }

.redactor-component-active[data-redactor-type=image] {
  outline: none !important; }
  .redactor-component-active[data-redactor-type=image] img {
    outline: 5px solid rgba(0, 125, 255, 0.5) !important; }

.redactor-component-active[data-redactor-type=variable] {
  outline: none !important;
  background: #ee698a; }

.redactor-component-active[data-redactor-type=video] {
  outline: none !important; }
  .redactor-component-active[data-redactor-type=video] iframe {
    outline: 5px solid rgba(0, 125, 255, 0.5) !important; }

.redactor-blur.redactor-styles-on .redactor-component-active {
  outline: 5px solid #ddd !important; }
  .redactor-blur.redactor-styles-on .redactor-component-active[data-redactor-type=image] {
    outline: none !important; }
    .redactor-blur.redactor-styles-on .redactor-component-active[data-redactor-type=image] img {
      outline: 5px solid #ddd !important; }
  .redactor-blur.redactor-styles-on .redactor-component-active[data-redactor-type=video] {
    outline: none !important; }
    .redactor-blur.redactor-styles-on .redactor-component-active[data-redactor-type=video] iframe {
      outline: 5px solid #ddd !important; }
  .redactor-blur.redactor-styles-on .redactor-component-active[data-redactor-type=variable] {
    outline: none !important;
    background: #ddd; }

.redactor-component-caret {
  position: absolute;
  left: -9999px; }

.redactor-textnodes-wrapper {
  display: inline-block; }

#redactor-image-resizer {
  position: absolute;
  z-index: 1051;
  background-color: rgba(0, 125, 255, 0.9);
  width: 13px;
  height: 13px;
  border: 1px solid #fff;
  cursor: move;
  cursor: nwse-resize; }

.redactor-file-item {
  display: inline-block;
  line-height: 1;
  padding: 4px 12px;
  border-radius: 16px;
  border: 1px solid rgba(0, 0, 0, 0.2); }

.redactor-file-remover {
  margin-left: 2px;
  position: relative;
  right: -3px;
  display: inline-block;
  padding: 0 3px;
  cursor: pointer;
  opacity: .5; }
  .redactor-file-remover:hover {
    opacity: 1; }

.redactor-overlay {
  position: fixed;
  z-index: 1051;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(16, 16, 18, 0.3); }
  .redactor-overlay > .redactor-close {
    position: fixed;
    top: 1rem;
    right: 1rem; }

.redactor-source,
.redactor-source:hover,
.redactor-source:focus {
  text-align: left;
  box-sizing: border-box;
  font-family: Consolas, Menlo, Monaco, "Courier New", monospace;
  width: 100%;
  display: block;
  margin: 0;
  border: none;
  box-shadow: none;
  border-radius: 0;
  background: #252525;
  color: #ccc;
  font-size: 15px;
  outline: none;
  padding: 10px 18px 20px 18px;
  line-height: 1.5;
  resize: vertical; }

.redactor-box[dir="rtl"] .redactor-source {
  direction: ltr; }

.redactor-placeholder:before {
  position: absolute;
  content: attr(placeholder);
  color: rgba(0, 0, 0, 0.4);
  font-weight: normal;
  cursor: text; }

.redactor-in figcaption[placeholder]:empty:before {
  content: attr(placeholder);
  color: rgba(0, 0, 0, 0.4);
  font-weight: normal; }

.redactor-in figcaption[placeholder]:empty:focus:before {
  content: ""; }

.redactor-statusbar {
  font-family: Consolas, Menlo, Monaco, "Courier New", monospace;
  margin: 0;
  padding: 8px 10px;
  position: relative;
  overflow: hidden;
  list-style: none;
  background: #f8f8f8;
  box-sizing: border-box;
  border: none; }
  .redactor-statusbar li {
    float: left;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.5);
    padding: 0 10px;
    line-height: 16px;
    border-right: 1px solid rgba(0, 0, 0, 0.1); }
  .redactor-statusbar li:last-child {
    border-right-color: transparent; }
  .redactor-statusbar a {
    color: rgba(0, 0, 0, 0.5);
    text-decoration: underline; }
  .redactor-statusbar a:hover {
    color: #f03c69;
    text-decoration: underline; }
  .redactor-statusbar:empty {
    display: none; }

.redactor-toolbar-wrapper {
  position: relative;
  z-index: 1; }

.redactor-toolbar,
.redactor-air {
  z-index: 100;
  font-family: "Trebuchet MS", "Helvetica Neue", Helvetica, Tahoma, sans-serif;
  position: relative;
  margin: 0 !important;
  padding: 0;
  list-style: none !important;
  line-height: 1 !important;
  background: none;
  border: none;
  box-sizing: border-box; }

.redactor-box.redactor-styles-on .redactor-toolbar {
  padding: 18px 16px 0 16px; }

.redactor-toolbar a,
.redactor-air a {
  display: inline-block;
  box-sizing: border-box;
  font-size: 14px;
  text-align: center;
  padding: 10px 15px 9px 15px;
  cursor: pointer;
  outline: none;
  border: none;
  vertical-align: middle;
  text-decoration: none;
  zoom: 1;
  position: relative;
  color: rgba(0, 0, 0, 0.85);
  border-radius: 2px;
  background: rgba(255, 255, 255, 0.97);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.22);
  margin-right: 5px;
  margin-bottom: 4px; }

.redactor-toolbar a.re-button-icon,
.redactor-air a.re-button-icon {
  font-size: 16px;
  padding: 9px 15px 6px 15px; }

.redactor-toolbar a:hover,
.redactor-air a:hover {
  outline: none;
  color: #fff;
  background: #449aef; }

.redactor-toolbar a.redactor-button-active {
  background: rgba(245, 245, 245, 0.95);
  color: rgba(0, 0, 0, 0.4); }

.redactor-toolbar a.redactor-button-disabled,
.redactor-air a.redactor-button-disabled {
  opacity: 0.3; }
  .redactor-toolbar a.redactor-button-disabled:hover,
  .redactor-air a.redactor-button-disabled:hover {
    color: #333;
    outline: none;
    background-color: transparent !important;
    cursor: default; }

.redactor-source-view .redactor-toolbar {
  background: #252525; }

.redactor-source-view .redactor-toolbar a {
  background: #000;
  color: #fff; }
  .redactor-source-view .redactor-toolbar a:hover {
    background: #449aef; }

.redactor-source-view .redactor-toolbar a.redactor-button-disabled:hover {
  color: #fff !important;
  background-color: #000 !important; }

.re-button-tooltip {
  display: none;
  position: absolute;
  white-space: nowrap;
  top: 0;
  z-index: 1052;
  background: rgba(0, 0, 0, 0.9);
  border-radius: 3px;
  padding: 5px 9px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
  line-height: 1;
  font-family: Consolas, Menlo, Monaco, "Courier New", monospace; }
  .re-button-tooltip:after {
    bottom: 100%;
    left: 50%;
    border: solid transparent;
    content: " ";
    height: 0;
    width: 0;
    position: absolute;
    pointer-events: none;
    border-color: none;
    border-bottom-color: rgba(0, 0, 0, 0.9);
    border-width: 4px;
    margin-left: -4px; }

.redactor-toolbar-wrapper-air {
  position: absolute;
  z-index: 100; }

.redactor-air {
  padding: 6px 3px 2px 8px;
  max-width: 576px;
  min-width: 200px;
  border-radius: 4px;
  background: rgba(0, 0, 0, 0.97); }

.redactor-air a {
  background: rgba(37, 37, 37, 0.95);
  box-shadow: none;
  color: rgba(255, 255, 255, 0.9); }

.redactor-air a:hover {
  background: #3d79f2; }

.redactor-air a.redactor-button-active {
  background-color: rgba(255, 255, 255, 0.15);
  color: #fff; }

.redactor-air a.redactor-button-disabled:hover {
  color: #fff; }

.redactor-air-helper {
  position: absolute;
  right: 0;
  top: 0;
  line-height: 1;
  font-size: 15px;
  color: #000;
  background: rgba(255, 255, 255, 0.85);
  border-bottom-left-radius: 4px;
  padding: 7px 10px 6px 10px;
  cursor: pointer; }
  .redactor-air-helper:hover {
    background: #fff; }

.redactor-voice-label {
  display: none; }

.redactor-context-toolbar {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1051;
  background-color: rgba(0, 0, 0, 0.95);
  color: #555;
  border-radius: 4px;
  padding: 6px 18px 7px 18px;
  line-height: 1.5;
  font-family: Consolas, Menlo, Monaco, "Courier New", monospace; }
  .redactor-context-toolbar a {
    font-size: 12px;
    color: #ccc;
    text-decoration: none;
    display: inline-block;
    padding: 2px 0 1px 12px; }
  .redactor-context-toolbar a:first-child {
    padding-left: 0; }
  .redactor-context-toolbar a i {
    position: relative;
    top: 3px;
    font-size: 16px; }
  .redactor-context-toolbar a:before {
    content: '';
    padding-left: 10px;
    border-left: 1px solid rgba(255, 255, 255, 0.3); }
  .redactor-context-toolbar a:hover {
    color: #fff; }
  .redactor-context-toolbar a:first-child:before {
    padding-left: 0;
    border-left: none; }

.redactor-context-toolbar[dir="rtl"] a {
  padding: 2px 12px 1px 0; }

.redactor-context-toolbar[dir="rtl"] a:first-child {
  padding-right: 0; }

.redactor-context-toolbar[dir="rtl"] a:before {
  padding-left: 0px;
  padding-right: 10px;
  border-right: 1px solid rgba(255, 255, 255, 0.3);
  border-left: none; }

.redactor-context-toolbar[dir="rtl"] a:first-child:before {
  padding-right: 0;
  border-right: none; }

.redactor-dropdown {
  font-family: "Trebuchet MS", "Helvetica Neue", Helvetica, Tahoma, sans-serif;
  display: none;
  position: absolute;
  z-index: 1051;
  background-color: #fff;
  box-shadow: 0 4px 14px rgba(0, 0, 0, 0.2);
  border-radius: 2px;
  width: 264px;
  min-height: 40px;
  max-height: 250px;
  margin: 0;
  margin-top: -1px;
  overflow: auto;
  font-size: 15px;
  padding: 0; }
  .redactor-dropdown a span {
    display: inline-block;
    line-height: 1;
    padding: 2px 4px;
    border-radius: 3px; }
  .redactor-dropdown a {
    display: block;
    text-decoration: none;
    padding: 10px 8px;
    white-space: nowrap;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05); }
  .redactor-dropdown a:last-child {
    border-bottom-color: transparent; }
  .redactor-dropdown a {
    color: #000; }
    .redactor-dropdown a:hover {
      color: #fff !important;
      background-color: #449aef !important; }
    .redactor-dropdown a.redactor-dropdown-item-disabled {
      color: rgba(0, 0, 0, 0.4);
      background: #fff; }

.redactor-dropdown-cells {
  margin: 10px auto; }
  .redactor-dropdown-cells a,
  .redactor-dropdown-cells span {
    float: left;
    cursor: pointer;
    box-sizing: border-box;
    text-align: center;
    padding: 0;
    margin: 0;
    font-size: 14px; }

.redactor-dropdown-selector {
  display: flex;
  text-align: center; }
  .redactor-dropdown-selector span {
    flex-grow: 1;
    font-size: 12px;
    padding: 8px;
    cursor: pointer; }
    .redactor-dropdown-selector span:hover {
      background: #eee; }
    .redactor-dropdown-selector span.active {
      cursor: text;
      color: rgba(0, 0, 0, 0.3);
      background: #eee; }

.redactor-dropdown-format .redactor-dropdown-item-blockquote {
  color: rgba(0, 0, 0, 0.4);
  font-style: italic; }

.redactor-dropdown-format .redactor-dropdown-item-pre {
  font-family: monospace, sans-serif; }

.redactor-dropdown-format .redactor-dropdown-item-h1 {
  font-size: 40px;
  font-weight: bold;
  line-height: 32px; }

.redactor-dropdown-format .redactor-dropdown-item-h2 {
  font-size: 32px;
  font-weight: bold;
  line-height: 32px; }

.redactor-dropdown-format .redactor-dropdown-item-h3 {
  font-size: 24px;
  font-weight: bold;
  line-height: 24px; }

.redactor-dropdown-format .redactor-dropdown-item-h4 {
  font-size: 21px;
  font-weight: bold;
  line-height: 24px; }

.redactor-dropdown-format .redactor-dropdown-item-h5 {
  font-size: 18px;
  font-weight: bold;
  line-height: 24px; }

.redactor-dropdown-format .redactor-dropdown-item-h6 {
  font-size: 14px;
  text-transform: uppercase;
  font-weight: bold;
  line-height: 24px; }

.redactor-modal-box {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  overflow-x: hidden;
  overflow-y: auto;
  z-index: 1051;
  font-family: "Trebuchet MS", "Helvetica Neue", Helvetica, Tahoma, sans-serif;
  line-height: 24px; }

.redactor-modal {
  position: relative;
  margin: 16px auto;
  padding: 0;
  background: #fff;
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.07), 0 2px 15px rgba(80, 80, 80, 0.25);
  border-radius: 3px;
  color: #000; }
  .redactor-modal form {
    margin: 0;
    padding: 0;
    box-sizing: border-box; }
  .redactor-modal input,
  .redactor-modal select,
  .redactor-modal textarea {
    box-sizing: border-box;
    display: block;
    width: 100%;
    font-family: inherit;
    font-size: 16px;
    height: 40px;
    outline: none;
    vertical-align: middle;
    background-color: #fff;
    border: 1px solid #cacfd4;
    border-radius: 0.1875em;
    box-shadow: none;
    padding: 0 .5em; }
  .redactor-modal textarea {
    padding: .5em;
    height: auto;
    line-height: 1.5;
    vertical-align: top; }
  .redactor-modal select {
    -webkit-appearance: none;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="9" height="12" viewBox="0 0 9 12"><path fill="rgba(0, 0, 0, .4);" d="M0.722,4.823L-0.01,4.1,4.134-.01,4.866,0.716Zm7.555,0L9.01,4.1,4.866-.01l-0.732.726ZM0.722,7.177L-0.01,7.9,4.134,12.01l0.732-.726Zm7.555,0L9.01,7.9,4.866,12.01l-0.732-.726Z"/></svg>');
    background-repeat: no-repeat;
    background-position: right .65em center;
    padding-right: 28px; }
  .redactor-modal select[multiple] {
    background-image: none;
    height: auto;
    padding: .5em .75em; }
  .redactor-modal input[type="file"] {
    width: auto;
    border: none;
    padding: 0;
    height: auto;
    background: none;
    box-shadow: none;
    display: inline-block; }
  .redactor-modal input[type="radio"],
  .redactor-modal input[type="checkbox"] {
    display: inline-block;
    width: auto;
    height: auto;
    padding: 0;
    vertical-align: middle;
    position: relative;
    bottom: 0.15rem;
    font-size: 115%;
    margin-right: 3px; }
  .redactor-modal .form-item {
    margin-bottom: 20px; }
  .redactor-modal .form-item:last-child {
    margin-bottom: 0; }
  .redactor-modal fieldset {
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 3px;
    padding: 16px;
    padding-bottom: 20px;
    margin-bottom: 20px; }
    .redactor-modal fieldset .form-item {
      margin-bottom: 12px; }
  .redactor-modal label {
    display: block;
    color: #555;
    margin-bottom: 0.25em;
    font-size: 14px; }
    .redactor-modal label .desc,
    .redactor-modal label .success,
    .redactor-modal label .error {
      text-transform: none;
      font-weight: normal; }
  .redactor-modal label.checkbox {
    font-size: 16px;
    line-height: 1.5;
    cursor: pointer;
    color: inherit; }
  .redactor-modal .form-checkboxes label.checkbox {
    display: inline-block;
    margin-right: 1em; }
  .redactor-modal input:hover,
  .redactor-modal textarea:hover,
  .redactor-modal select:hover {
    outline: none;
    background-color: #fff;
    border-color: #969fa9;
    box-shadow: none; }
  .redactor-modal input:focus,
  .redactor-modal textarea:focus,
  .redactor-modal select:focus {
    transition: all linear .2s;
    outline: none;
    background-color: #fff;
    border-color: rgba(0, 125, 255, 0.5);
    box-shadow: 0 0 3px rgba(0, 125, 255, 0.5); }
  .redactor-modal input.error,
  .redactor-modal textarea.error,
  .redactor-modal select.error {
    background-color: rgba(255, 50, 101, 0.1);
    border: 1px solid #ff7f9e; }
    .redactor-modal input.error:focus,
    .redactor-modal textarea.error:focus,
    .redactor-modal select.error:focus {
      border-color: #ff3265;
      box-shadow: 0 0 1px #ff3265; }
  .redactor-modal input.success,
  .redactor-modal textarea.success,
  .redactor-modal select.success {
    background-color: rgba(47, 196, 182, 0.1);
    border: 1px solid #65dacf; }
    .redactor-modal input.success:focus,
    .redactor-modal textarea.success:focus,
    .redactor-modal select.success:focus {
      border-color: #2fc4b6;
      box-shadow: 0 0 1px #2fc4b6; }
  .redactor-modal input:disabled, .redactor-modal input:disabled:hover, .redactor-modal input.disabled, .redactor-modal input.disabled:hover,
  .redactor-modal textarea:disabled,
  .redactor-modal textarea:disabled:hover,
  .redactor-modal textarea.disabled,
  .redactor-modal textarea.disabled:hover,
  .redactor-modal select:disabled,
  .redactor-modal select:disabled:hover,
  .redactor-modal select.disabled,
  .redactor-modal select.disabled:hover {
    resize: none;
    opacity: .6;
    cursor: default;
    font-style: italic;
    color: rgba(0, 0, 0, 0.5);
    border: 1px solid #cacfd4;
    box-shadow: none;
    background-color: #fff; }
  .redactor-modal .req {
    position: relative;
    top: 1px;
    font-weight: bold;
    color: #ff3265;
    font-size: 110%; }
  .redactor-modal .desc {
    color: rgba(51, 51, 51, 0.5);
    font-size: 12px; }
  .redactor-modal span.desc {
    margin-left: 0.25em; }
  .redactor-modal div.desc {
    margin-top: 0.25em; }
  .redactor-modal span.success,
  .redactor-modal span.error {
    font-size: 12px;
    margin-left: 0.25em; }
  .redactor-modal div.desc {
    margin-bottom: -0.5em; }
  .redactor-modal .redactor-close {
    position: absolute;
    top: 16px;
    right: 12px;
    font-size: 30px;
    line-height: 30px;
    padding: 0px 4px;
    color: #000;
    opacity: .3;
    cursor: pointer; }
    .redactor-modal .redactor-close:hover {
      opacity: 1; }
    .redactor-modal .redactor-close:before {
      content: '\00d7'; }
  .redactor-modal button {
    display: inline-flex;
    align-items: center;
    text-decoration: none;
    text-align: center;
    font-family: inherit;
    font-size: 15px;
    font-weight: 500;
    color: #007dff;
    background-color: #fff;
    border-radius: 3px;
    border: 2px solid #007dff;
    min-height: 40px;
    outline: none;
    padding: 0.5em 1.25em;
    cursor: pointer;
    line-height: 1.2;
    vertical-align: middle;
    -webkit-appearance: none; }
  .redactor-modal button:hover {
    outline: none;
    text-decoration: none;
    background: none;
    color: rgba(0, 125, 255, 0.6);
    border-color: rgba(0, 125, 255, 0.5); }
  .redactor-modal button.redactor-button-secondary {
    border-color: #2a2e34;
    color: #2a2e34; }
    .redactor-modal button.redactor-button-secondary:hover {
      color: rgba(42, 46, 52, 0.6);
      border-color: rgba(42, 46, 52, 0.5); }
  .redactor-modal button.redactor-button-danger,
  .redactor-modal button.redactor-button-unstyled {
    background: none;
    border-color: transparent;
    color: rgba(42, 46, 52, 0.6); }
    .redactor-modal button.redactor-button-danger:hover,
    .redactor-modal button.redactor-button-unstyled:hover {
      background: none;
      border-color: transparent;
      color: #ff3265;
      text-decoration: underline; }
  .redactor-modal .redactor-modal-group:after {
    content: "";
    display: table;
    clear: both; }
  .redactor-modal .redactor-modal-side {
    float: left;
    width: 30%;
    margin-right: 4%; }
    .redactor-modal .redactor-modal-side img {
      max-width: 100%;
      height: auto;
      display: block; }
  .redactor-modal .redactor-modal-area {
    float: left;
    width: 66%; }

.redactor-modal[dir="rtl"] .redactor-close {
  left: 12px;
  right: auto; }

.redactor-modal[dir="rtl"] textarea {
  direction: ltr;
  text-align: left; }

.redactor-modal[dir="rtl"] .redactor-modal-footer button.redactor-button-unstyled {
  float: left;
  margin-left: 0; }

.redactor-modal-header {
  padding: 20px;
  font-size: 18px;
  line-height: 24px;
  font-weight: bold;
  color: #000;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05); }
  .redactor-modal-header:empty {
    display: none; }

.redactor-modal-body {
  padding: 32px 48px;
  padding-bottom: 40px; }

.redactor-modal-footer {
  padding: 24px;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  overflow: hidden; }
  .redactor-modal-footer button {
    margin-right: 4px; }
  .redactor-modal-footer button.redactor-button-unstyled {
    margin-right: 0;
    float: right; }
  .redactor-modal-footer:empty {
    display: none; }

.redactor-modal-tabs {
  display: flex;
  border-bottom: 2px solid rgba(0, 0, 0, 0.05);
  margin-bottom: 1em; }
  .redactor-modal-tabs a {
    font-size: 15px;
    padding: 2px 0;
    text-decoration: none;
    color: rgba(0, 0, 0, 0.5);
    border-bottom: 2px solid transparent;
    margin-bottom: -2px;
    margin-right: 14px; }
  .redactor-modal-tabs a:hover {
    transition: all linear .2s; }
  .redactor-modal-tabs a:hover,
  .redactor-modal-tabs a.active {
    font-weight: 500;
    color: #007dff;
    border-bottom-color: #007dff; }

.redactor-styles {
  margin: 0;
  padding: 16px 18px;
  color: #333;
  font-family: "Trebuchet MS", "Helvetica Neue", Helvetica, Tahoma, sans-serif;
  font-size: 1em;
  line-height: 1.5;
  box-sizing: border-box; }
  .redactor-styles *,
  .redactor-styles *:before,
  .redactor-styles *:after {
    box-sizing: inherit; }
  .redactor-styles[dir="rtl"] {
    direction: rtl;
    unicode-bidi: embed; }
    .redactor-styles[dir="rtl"] ul li,
    .redactor-styles[dir="rtl"] ol li {
      text-align: right; }
    .redactor-styles[dir="rtl"] ul,
    .redactor-styles[dir="rtl"] ol,
    .redactor-styles[dir="rtl"] ul ul,
    .redactor-styles[dir="rtl"] ol ol,
    .redactor-styles[dir="rtl"] ul ol,
    .redactor-styles[dir="rtl"] ol ul {
      margin-left: 1.5em; }
    .redactor-styles[dir="rtl"] figcaption {
      text-align: right; }
  .redactor-styles ul[dir="rtl"],
  .redactor-styles ul[dir="rtl"] ul,
  .redactor-styles ul[dir="rtl"] ol,
  .redactor-styles ol[dir="rtl"],
  .redactor-styles ol[dir="rtl"] ul,
  .redactor-styles ol[dir="rtl"] ol {
    margin-right: 1.5em; }
  .redactor-styles ul[dir="rtl"] li,
  .redactor-styles ol[dir="rtl"] li {
    text-align: right; }
  .redactor-styles a,
  .redactor-styles a:hover {
    color: #3397ff; }
  .redactor-styles p,
  .redactor-styles dl,
  .redactor-styles blockquote,
  .redactor-styles hr,
  .redactor-styles pre,
  .redactor-styles table,
  .redactor-styles figure,
  .redactor-styles address {
    padding: 0;
    margin: 0;
    margin-bottom: 1em; }
  .redactor-styles ul,
  .redactor-styles ol {
    padding: 0; }
    .redactor-styles ul,
    .redactor-styles ul ul,
    .redactor-styles ul ol,
    .redactor-styles ol,
    .redactor-styles ol ul,
    .redactor-styles ol ol {
      margin: 0 0 0 1.5em; }
  .redactor-styles ul li,
  .redactor-styles ol li {
    text-align: left; }
  .redactor-styles ol ol li {
    list-style-type: lower-alpha; }
  .redactor-styles ol ol ol li {
    list-style-type: lower-roman; }
  .redactor-styles ul,
  .redactor-styles ol {
    margin-bottom: 1em; }
  .redactor-styles h1,
  .redactor-styles h2,
  .redactor-styles h3,
  .redactor-styles h4,
  .redactor-styles h5,
  .redactor-styles h6 {
    font-weight: bold;
    color: #111;
    text-rendering: optimizeLegibility;
    margin: 0;
    padding: 0;
    margin-bottom: 0.5em;
    line-height: 1.2; }
  .redactor-styles h1 {
    font-size: 2.0736em; }
  .redactor-styles h2 {
    font-size: 1.728em; }
  .redactor-styles h3 {
    font-size: 1.44em; }
  .redactor-styles h4 {
    font-size: 1.2em; }
  .redactor-styles h5 {
    font-size: 1em; }
  .redactor-styles h6 {
    font-size: 0.83333em;
    text-transform: uppercase;
    letter-spacing: .035em; }
  .redactor-styles blockquote {
    font-style: italic;
    color: rgba(0, 0, 0, 0.5);
    border: none; }
  .redactor-styles table {
    width: 100%; }
  .redactor-styles time, .redactor-styles small, .redactor-styles var, .redactor-styles code, .redactor-styles kbd, .redactor-styles mark {
    display: inline-block;
    font-family: Consolas, Menlo, Monaco, "Courier New", monospace;
    font-size: 87.5%;
    line-height: 1;
    color: rgba(51, 51, 51, 0.9); }
  .redactor-styles var, .redactor-styles cite {
    opacity: .6; }
  .redactor-styles var {
    font-style: normal; }
  .redactor-styles dfn,
  .redactor-styles abbr {
    text-transform: uppercase; }
    .redactor-styles dfn[title],
    .redactor-styles abbr[title] {
      text-decoration: none;
      border-bottom: 1px dotted rgba(0, 0, 0, 0.5);
      cursor: help; }
  .redactor-styles code, .redactor-styles kbd {
    position: relative;
    top: -1px;
    padding: 0.25em;
    padding-bottom: 0.2em;
    border-radius: 2px; }
  .redactor-styles code {
    background-color: #eff1f2; }
  .redactor-styles mark {
    border-radius: 2px;
    padding: 0.125em 0.25em;
    background-color: #fdb833; }
  .redactor-styles kbd {
    border: 1px solid #e5e7e9; }
  .redactor-styles sub,
  .redactor-styles sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline; }
  .redactor-styles sub {
    bottom: -0.25em; }
  .redactor-styles sup {
    top: -0.5em; }
  .redactor-styles pre {
    font-family: Consolas, Menlo, Monaco, "Courier New", monospace;
    font-size: .9em; }
  .redactor-styles pre,
  .redactor-styles pre code {
    background-color: #f6f7f8;
    padding: 0;
    top: 0;
    display: block;
    line-height: 1.5;
    color: rgba(51, 51, 51, 0.85);
    overflow: none;
    white-space: pre-wrap; }
  .redactor-styles pre {
    padding: 1rem; }
  .redactor-styles table {
    border-collapse: collapse;
    max-width: 100%;
    width: 100%; }
    .redactor-styles table caption {
      text-transform: uppercase;
      padding: 0;
      color: rgba(0, 0, 0, 0.5);
      font-size: 11px; }
    .redactor-styles table th,
    .redactor-styles table td {
      border: 1px solid #eee;
      padding: 16px;
      padding-bottom: 15px; }
    .redactor-styles table tfoot th,
    .redactor-styles table tfoot td {
      color: rgba(0, 0, 0, 0.5); }
  .redactor-styles img,
  .redactor-styles video,
  .redactor-styles audio,
  .redactor-styles embed,
  .redactor-styles object {
    max-width: 100%; }
  .redactor-styles img,
  .redactor-styles video,
  .redactor-styles embed,
  .redactor-styles object {
    height: auto !important; }
  .redactor-styles img {
    vertical-align: middle;
    -ms-interpolation-mode: bicubic; }
  .redactor-styles figcaption {
    display: block;
    opacity: .6;
    font-size: 12px;
    font-style: italic;
    text-align: left; }

.upload-redactor-box {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
  border: 5px dashed rgba(0, 125, 255, 0.15);
  position: relative;
  width: 100%;
  min-height: 220px;
  background: #fff;
  cursor: pointer;
  overflow: hidden;
  text-align: center; }

.upload-redactor-placeholder {
  font-size: 15px;
  line-height: 1.5;
  color: rgba(0, 0, 0, 0.3);
  font-style: italic; }

.upload-redactor-hover {
  background-color: rgba(0, 125, 255, 0.075); }

.upload-redactor-error {
  background-color: rgba(255, 50, 101, 0.075); }

.upload-redactor-box-hover {
  outline: 5px dashed rgba(0, 125, 255, 0.3); }

.upload-redactor-box-error {
  outline: 5px dashed rgba(255, 50, 101, 0.3); }

.redactor-structure h1, .redactor-structure h2, .redactor-structure h3, .redactor-structure h4, .redactor-structure h5, .redactor-structure h6, .redactor-structure div {
  position: relative; }
  .redactor-structure h1:before, .redactor-structure h2:before, .redactor-structure h3:before, .redactor-structure h4:before, .redactor-structure h5:before, .redactor-structure h6:before, .redactor-structure div:before {
    width: 24px;
    position: absolute;
    font-size: 10px;
    font-weight: normal;
    opacity: .5;
    left: -26px;
    top: 50%;
    margin-top: -7px;
    text-align: right; }

.redactor-structure h1:before {
  content: "h1"; }

.redactor-structure h2:before {
  content: "h2"; }

.redactor-structure h3:before {
  content: "h3"; }

.redactor-structure h4:before {
  content: "h4"; }

.redactor-structure h5:before {
  content: "h5"; }

.redactor-structure h6:before {
  content: "h6"; }

.redactor-structure div:before {
  content: "div"; }

#redactor-progress {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1000000;
  height: 10px; }

#redactor-progress span {
  background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);
  -webkit-animation: progress-bar-stripes 2s linear infinite;
  -o-animation: progress-bar-stripes 2s linear infinite;
  animation: progress-bar-stripes 2s linear infinite;
  display: block;
  width: 100%;
  height: 100%;
  background-color: #007dff;
  background-size: 40px 40px; }

@-webkit-keyframes progress-bar-stripes {
  from {
    background-position: 40px 0; }
  to {
    background-position: 0 0; } }

@-o-keyframes progress-bar-stripes {
  from {
    background-position: 40px 0; }
  to {
    background-position: 0 0; } }

@keyframes progress-bar-stripes {
  from {
    background-position: 40px 0; }
  to {
    background-position: 0 0; } }

.redactor-box-fullscreen {
  z-index: 1051;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%; }

.redactor-box-fullscreen-target {
  position: absolute !important; }

.redactor-body-fullscreen .redactor-dropdown,
.redactor-body-fullscreen .redactor-context-toolbar {
  z-index: 1052; }

.redactor-body-fullscreen #redactor-overlay {
  z-index: 1098; }

.redactor-body-fullscreen #redactor-modal {
  z-index: 1099; }

/** osTicket-specific customizations */
/* === A classic toolbar ==== */
.redactor-toolbar.redactor-toolbar a {
  margin: 0;
  margin-bottom: 5px;
  border-radius: 0;
  box-shadow: none;
  background-color: none;
}

.redactor-box.redactor-styles-on .redactor-toolbar.redactor-toolbar {
  padding: 4px 4px 0 4px;
  box-shadow: 0px 4px 10px -6px rgba(0, 0, 0, 0.22);
  background-color: rgba(255, 255, 255, 0.97);
}

.redactor-box.redactor-styles-on.redactor-source-view .redactor-toolbar.redactor-toolbar {
  background-color: rgba(0, 0, 0, 0.97);
}

.redactor-toolbar a.re-button-icon.re-button-icon,
.redactor-air a.re-button-icon.re-button-icon {
  padding: 9px 12px 6px 12px;
}

.redactor-in[style*=' width:'], .redactor-in[style^='width:'] {
  border-right: 1px dashed #999;
}

.redactor-toolbar.redactor-toolbar,
.redactor-air.redactor-air {
  z-index: 9;
}

.redactor-modal .redactor-toolbar {
    border-bottom: 1px solid rgba(0,0,0,.05);
    box-shadow: 0 3px 5px -5px rgba(0,0,0,.5);
}
