<?php return array(
    'root' => array(
        'name' => 'laminas/laminas-mail',
        'pretty_version' => '1.0.0+no-version-set',
        'version' => '1.0.0.0',
        'reference' => NULL,
        'type' => 'library',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => false,
    ),
    'versions' => array(
        'container-interop/container-interop' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '^1.2.0',
            ),
        ),
        'laminas/laminas-loader' => array(
            'pretty_version' => '2.10.0',
            'version' => '2.10.0.0',
            'reference' => 'e6fe952304ef40ce45cd814751ab35d42afdad12',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laminas/laminas-loader',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laminas/laminas-mail' => array(
            'pretty_version' => '1.0.0+no-version-set',
            'version' => '1.0.0.0',
            'reference' => NULL,
            'type' => 'library',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laminas/laminas-mime' => array(
            'pretty_version' => '2.12.0',
            'version' => '2.12.0.0',
            'reference' => '08cc544778829b7d68d27a097885bd6e7130135e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laminas/laminas-mime',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laminas/laminas-servicemanager' => array(
            'pretty_version' => '3.22.1',
            'version' => '3.22.1.0',
            'reference' => 'de98d297d4743956a0558a6d71616979ff779328',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laminas/laminas-servicemanager',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laminas/laminas-stdlib' => array(
            'pretty_version' => '3.19.0',
            'version' => '3.19.0.0',
            'reference' => '6a192dd0882b514e45506f533b833b623b78fff3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laminas/laminas-stdlib',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laminas/laminas-validator' => array(
            'pretty_version' => '2.55.0',
            'version' => '2.55.0.0',
            'reference' => 'dc3f2609d41b1e21bc24e3e147d7dd284e8a1556',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laminas/laminas-validator',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/container' => array(
            'pretty_version' => '1.1.2',
            'version' => '1.1.2.0',
            'reference' => '513e0666f7216c7459170d56df27dfcefe1689ea',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/container',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/container-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '^1.0',
            ),
        ),
        'psr/http-message' => array(
            'pretty_version' => '2.0',
            'version' => '2.0.0.0',
            'reference' => '402d35bcb92c70c026d1a6a9883f06b2ead23d71',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-message',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-idn' => array(
            'pretty_version' => 'v1.29.0',
            'version' => '1.29.0.0',
            'reference' => 'a287ed7475f85bf6f61890146edbc932c0fff919',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-idn',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-normalizer' => array(
            'pretty_version' => 'v1.29.0',
            'version' => '1.29.0.0',
            'reference' => 'bc45c394692b948b4d383a08d7753968bed9a83d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-normalizer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-mbstring' => array(
            'pretty_version' => 'v1.29.0',
            'version' => '1.29.0.0',
            'reference' => '9773676c8a1bb1f8d4340a62efe641cf76eda7ec',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-mbstring',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php72' => array(
            'pretty_version' => 'v1.29.0',
            'version' => '1.29.0.0',
            'reference' => '861391a8da9a04cbad2d232ddd9e4893220d6e25',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php72',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'webmozart/assert' => array(
            'pretty_version' => '1.11.0',
            'version' => '1.11.0.0',
            'reference' => '11cb2199493b2f8a3b53e7f19068fc6aac760991',
            'type' => 'library',
            'install_path' => __DIR__ . '/../webmozart/assert',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
