#
# This is popup help messages for the Admin Panel -> Settings -> Tickets
#
# Fields:
# title - Shown in bold at the top of the popover window
# content - The body of the help popover
# links - List of links shows below the content
#   title - Link title
#   href - href of link (links starting with / are translated to the
#       helpdesk installation path)
#
# The key names such as 'helpdesk_name' should not be translated as they
# must match the HTML #ids put into the page template.
#
---
number_format:
    title: Ticket Number Format
    content: >
        This setting is used to generate ticket numbers. Use hash signs
        (`#`) where digits are to be placed. Any other text in the number
        format will be preserved. <span class="doc-desc-title">Help
        Topics</span> can define custom number formats.
        <br/><br/>
        For example, for six-digit numbers, use <code>######</code>.

sequence_id:
    title: Ticket Number Sequence
    content: >
        Choose a sequence from which to derive new ticket numbers. The
        system has a incrementing sequence and a random sequence by default.
        You may create as many sequences as you wish. Use various sequences
        in the <span class="doc-desc-title">Ticket Number
        Format</span> configuration for help topics.

queue_bucket_counts:
    title: Top-Level Ticket Counts
    content: >
        This setting is used to hide or show the ticket counts on Main-Level
        queues. Get back to the way things used to be.

default_ticket_status:
    title: Default Status for new Tickets
    content: >
        Choose a status as the default for new tickets. This can be defined
        for each help topic, if desired. It can also be overridden by a
        ticket filter.
    links:
      - title: Manage Ticket Statuses
        href: /scp/lists.php?type=ticket-status

default_sla:
    title: Default SLA
    content: >
        Choose the default Service Level Agreement to manage how long a ticket
        can remain Open before it is rendered Overdue.
    links:
      - title: Create more SLA Plans
        href: /scp/slas.php

default_ticket_queue:
    title: Default Ticket Queue
    content: >
        Setting to determine the default queue for Agents upon log-in.
        Agents can also set their default queue in their Profile tab to
        override this setting.

default_priority:
    title: Default Priority
    content: >
        Choose a default <span class="doc-desc-title">priority</span> for
        tickets not assigned a priority automatically.
        <br/><br/>
        Priority can be assigned via the help topic, routed department, or
        ticket filter settings.

maximum_open_tickets:
    title: Maximum Open Tickets
    content: >
        Enter the maximum <em>number</em> of tickets a User is permitted to
        have <strong>open</strong> in your help desk.
        <br><br>
        Enter <span class="doc-desc-opt">0 </span> if you prefer to disable this limitation.

email_ticket_priority:
    title: Email Ticket Priority
    content: >
        Use email priority assigned by addressee’s mail service

show_related_tickets:
    title: Show Related Tickets
    content: >
        Show all related tickets on user login - otherwise access is restricted to
        one ticket view per login

human_verification:
    title: Human Verification
    content: >
        Enable CAPTCHA on the Client Portal to verify an incoming ticket is the
        result of human activity.
        <br><br>
        Requires GDLib library

claim_tickets:
    title: Claim Tickets on Response
    content: >
        Enable this to auto-assign unassigned tickets to the responding Agent.
        <br><br>
        Reopened tickets are always assigned to the last respondent unless auto
        assign on reopen is disabled on the Department level.

auto_refer:
    title: Auto-refer Tickets on Close
    content: >
        Enable this to auto-refer tickets to the assigned or closing
        Agent when a ticket is closed.
        <br><br>
        This is necessary when you want to give agents with limited access
        continued access to assigned tickets after they're closed.

collaborator_ticket_visibility:
    title: Collaborator Tickets Visibility
    content: >
        If Enabled, Users will have visibility to ALL Tickets they participate in
        when signing into the Web Portal.
        <br><br>
        If Disabled, Users will only be able to see their own Tickets
        when signing into the Web Portal.

require_topic_to_close:
    title: Require Help Topic to Close
    content: >
        If Enabled, a Ticket must have a Help Topic in order to be Closed by an Agent

allow_external_images:
    title: Allow External Images
    content: >
        If Enabled, the system will allow external inline images that have a valid image
        extension (.png, .jpg, .jpeg, .gif). If Disabled, the system will exclude
        any external inline images. One caveat to note, is if the setting is Disabled we
        will still store external inline images that have a valid image extension in case
        the setting is re-enabled in the future.

assigned_tickets:
    title: Assigned Tickets
    content: >
        Enable this feature to exclude assigned tickets from the <span class="doc-desc-title">Open
        Tickets Queue</span>.

answered_tickets:
    title: Answered Tickets
    content: >
        Enable this feature to show answered tickets in the <span
        class="doc-desc-title">Answered Tickets Queue</span>. Otherwise, it
        will be included in the <span class="doc-desc-title">Open Tickets
        Queue</span>.

ticket_attachment_settings:
    title: Ticket Thread Attachments
    content: >
        Configure settings for files attached to the <span
        class="doc-desc-title">issue details</span> field. These settings
        are used for all new tickets and new messages regardless of the
        source channel (web portal, email, api, etc.).
