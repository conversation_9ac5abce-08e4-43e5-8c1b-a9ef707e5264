#
# Page template: landing.yaml
#
# The landing page is served on the front page of the customer portal above
# the two links for creating new tickets and viewing ticket status.
#
---
notes: >
    The Landing Page refers to the content of the Customer Portal's
    initial view. The template modifies the content seen above the two links
    <strong>Open a New Ticket</strong> and <strong>Check Ticket Status</strong>.

name: Landing
body: >
    <h1>Welcome to the Support Center</h1>
    <p>
    In order to streamline support requests and better serve you, we utilize
    a support ticket system. Every support request is assigned a unique
    ticket number which you can use to track the progress and responses
    online. For your reference we provide complete archives and history of
    all your support requests. A valid email address is required to submit a
    ticket.
    </p>
