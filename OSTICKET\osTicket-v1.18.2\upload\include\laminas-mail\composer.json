{"name": "laminas/laminas-mail", "description": "Provides generalized functionality to compose and send both text and MIME-compliant multipart e-mail messages", "keywords": ["laminas", "mail"], "homepage": "https://laminas.dev", "license": "BSD-3-<PERSON><PERSON>", "require": {"php": "~8.1.0 || ~8.2.0 || ~8.3.0", "ext-iconv": "*", "laminas/laminas-loader": "^2.9.0", "laminas/laminas-mime": "^2.11.0", "laminas/laminas-servicemanager": "^3.22", "laminas/laminas-stdlib": "^3.17.0", "laminas/laminas-validator": "^2.31.0", "symfony/polyfill-intl-idn": "^1.27.0", "symfony/polyfill-mbstring": "^1.27.0", "webmozart/assert": "^1.11.0"}, "require-dev": {"laminas/laminas-coding-standard": "~2.5.0", "laminas/laminas-db": "^2.18", "phpunit/phpunit": "^10.4.2", "psalm/plugin-phpunit": "^0.18.4", "symfony/process": "^6.4", "vimeo/psalm": "^5.15"}, "suggest": {"laminas/laminas-servicemanager": "^3.21 when using SMTP to deliver messages"}, "config": {"sort-packages": true, "allow-plugins": {"composer/package-versions-deprecated": true, "dealerdirect/phpcodesniffer-composer-installer": true}, "platform": {"php": "8.1.99"}}, "extra": {"laminas": {"component": "Laminas\\Mail", "config-provider": "Laminas\\Mail\\ConfigProvider"}}, "autoload": {"psr-4": {"Laminas\\Mail\\": "src/"}}, "autoload-dev": {"psr-4": {"LaminasTest\\Mail\\": "test/"}}, "scripts": {"check": ["@cs-check", "@static-analysis", "@test"], "cs-check": "phpcs", "cs-fix": "phpcbf", "static-analysis": "psalm --shepherd --stats", "test": "phpunit --colors=always", "test-coverage": "phpunit --colors=always --coverage-clover clover.xml"}, "support": {"issues": "https://github.com/laminas/laminas-mail/issues", "forum": "https://discourse.laminas.dev", "chat": "https://laminas.dev/chat", "source": "https://github.com/laminas/laminas-mail", "docs": "https://docs.laminas.dev/laminas-mail/", "rss": "https://github.com/laminas/laminas-mail/releases.atom"}}