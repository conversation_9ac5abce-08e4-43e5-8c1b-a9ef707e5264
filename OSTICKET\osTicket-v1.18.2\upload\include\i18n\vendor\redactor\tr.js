(function($R)
{
    $R.lang['tr'] = {
        "format": "Format",
        "image": "<PERSON><PERSON><PERSON><PERSON>",
        "file": "<PERSON><PERSON><PERSON>",
        "link": "<PERSON>",
        "bold": "<PERSON><PERSON>ı<PERSON>",
        "italic": "<PERSON><PERSON><PERSON>",
        "deleted": "Üzeri çizgili",
        "underline": "Altı çizgili",
        "superscript": "Superscript",
        "subscript": "Subscript",
        "bold-abbr": "B",
        "italic-abbr": "I",
        "deleted-abbr": "S",
        "underline-abbr": "U",
        "superscript-abbr": "Sup",
        "subscript-abbr": "Sub",
        "lists": "Listeleme",
        "link-insert": "<PERSON> ekle",
        "link-edit": "Linki düzenle",
        "link-in-new-tab": "<PERSON>ni bir pencerede aç",
        "unlink": "Linki Kaldır",
        "cancel": "Vazgeç",
        "close": "<PERSON><PERSON><PERSON>",
        "insert": "<PERSON><PERSON>",
        "save": "<PERSON><PERSON>",
        "delete": "Sil",
        "text": "Metin",
        "edit": "<PERSON>üzenle",
        "title": "<PERSON>şlı<PERSON>",
        "paragraph": "<PERSON> yazı",
        "quote": "Alıntı",
        "code": "Kod",
        "heading1": "Başlık 1",
        "heading2": "Başlık 2",
        "heading3": "Başlık 3",
        "heading4": "Başlık 4",
        "heading5": "Başlık 5",
        "heading6": "Başlık 6",
        "filename": "İsim",
        "optional": "opsiyonel",
        "unorderedlist": "Sırasız Liste",
        "orderedlist": "Sıralı Liste",
        "outdent": "Dışarı Doğru",
        "indent": "İçeri Doğru",
        "horizontalrule": "Çizgi",
        "upload": "Upload",
        "upload-label": "Drop files here or click to upload",
        "upload-change-label": "Drop a new image to change",
        "accessibility-help-label": "Zenginleştirilmiş yazı editorü",
        "caption": "Caption",
        "bulletslist": "Bullets",
        "numberslist": "Numbers",
        "image-position": "Position",
        "none": "None",
        "left": "Left",
        "right": "Right",
        "center": "Center",
        "undo": "Undo",
        "redo": "Redo"
    };
})(Redactor);