#!/usr/bin/env php
<?php

declare(strict_types=1);

namespace Laminas\ServiceManager;

// Setup/verify autoloading
if (file_exists($a = getcwd() . '/vendor/autoload.php')) {
    require $a;
} elseif (file_exists($a = __DIR__ . '/../../../autoload.php')) {
    require $a;
} elseif (file_exists($a = __DIR__ . '/../vendor/autoload.php')) {
    require $a;
} else {
    fwrite(STDERR, 'Cannot locate autoloader; please run "composer install"' . PHP_EOL);
    exit(1);
}

$command = new Tool\ConfigDumperCommand($argv[0]);
$status = $command(array_slice($argv, 1));
exit($status);
