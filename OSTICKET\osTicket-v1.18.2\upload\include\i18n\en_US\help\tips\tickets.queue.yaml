#
# This is popup help messages for the Staff Panel -> Tickets -> Open
#
# Fields:
# title - Shown in bold at the top of the popover window
# content - The body of the help popover
# links - List of links shows below the content
#   title - Link title
#   href - href of link (links starting with / are translated to the
#       helpdesk installation path)
#
# The key names such as 'helpdesk_name' should not be translated as they
# must match the HTML #ids put into the page template.
#
---
search_field:
    title: Search Field
    content: >

advanced:
    title: Advanced
    content: >
        Narrow down your search parameters. Once you have selected your advanced
        search criteria and run the search, you can <span class="doc-desc-title">Export
        </span> the data at the bottom of the tickets page.

open_tickets_table:
    title: Open Tickets Table
    content: >
        All tickets currently opened and requiring attention

ticket:
    title: Ticket
    content: >

date:
    title: Date
    content: >

subject:
    title: Subject
    content: >

from:
    title: From
    content: >

priority:
    title: Priority
    content: >

assigned_to:
    title: Assigned To
    content: >

export:
    title: Export
    content: >
        Export your data currently in view in a CSV file.
        CSV files may be opened with any spreadsheet software
        (i.e., Microsoft Excel, Apple Pages, OpenOffice, etc.).

advanced_search_dialog:
    title: Advanced Search
    content: >


adv_keyword:
    title: Keyword Search
    content: >
        Find hits based on the subject and message bodies of the ticket
        thread as well as all textual content associated with custom fields
        for the users and the tickets.

adv_date_range:
    title: Search by Date Range
    content: >
        Definition here

merge_types:
    title: Merge Types
    content: >
        <b>Combine Threads:</b>
          Threads from all Tickets will be displayed chronologically.</br>
        <b>Separate Threads:</b>
          Threads from Tickets will be displayed one Ticket at a time.

child_status:
    title: Child Ticket Status
    content: >
        All Child Tickets will be set to a closed status since thread entries will all be moved to the Parent Ticket.

parent_status:
    title: Parent Ticket Status
    content: >
        If you choose to set a Parent Status, the Parent Ticket will be changed to the status you select.
        The Ticket on top of the list will be the Parent Ticket.

reply_types:
    title: Reply Types
    content: >
        <b>Reply All:</b>
          This reply is sent to the User and the Collaborators you choose to include.</br>
        <b>Reply to User:</b>
          This reply is sent to the User only, no Collaborators.</br>
        <b>Do Not Email Reply:</b>
          No email alerts are sent out, however, the Agent response is visible to <b>ALL</b> Users upon viewing the Ticket.
