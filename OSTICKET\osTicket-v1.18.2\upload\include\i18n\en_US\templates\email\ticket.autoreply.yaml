#
# Email template: ticket.autoreply.yaml
#
# Sent to a user when an automatic canned response is posted to a ticket
# when it is created
#
---
notes: |
    Sent to a user when an automatic canned response is posted to a ticket
    when it is created.

    Available variables for replacement: %{ticket.*}, %{response}

subject: |
    Re: %{ticket.subject} [#%{ticket.number}]
body: |
    <h3><strong>Dear %{recipient.name.first},</strong></h3>
    A request for support has been created and assigned ticket <a
    href="%{recipient.ticket_link}">#%{ticket.number}</a> with the following
    automatic reply
    <br>
    <br>
    Topic: <strong>%{ticket.topic.name}</strong>
    <br>
    Subject: <strong>%{ticket.subject}</strong>
    <br>
    <br>
    %{response}
    <br>
    <br>
    <div style="color: rgb(127, 127, 127);">Your %{company.name} Team,<br>
    %{signature}</div>
    <hr>
    <div style="color: rgb(127, 127, 127); font-size: small;"><em>We hope
    this response has sufficiently answered your questions.  If you wish to
    provide additional comments or information, please reply to this email
    or <a href="%{recipient.ticket_link}"><span
    style="color: rgb(84, 141, 212);" >login to your account</span></a> for
    a complete archive of your support requests.</em></div>
