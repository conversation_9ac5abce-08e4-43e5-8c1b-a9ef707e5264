#
# This is popup help messages for the Admin Panel -> Dashboard  -> System Logs page
#
# Fields:
# title - Shown in bold at the top of the popover window
# content - The body of the help popover
# links - List of links shows below the content
#   title - Link title
#   href - href of link (links starting with / are translated to the
#       helpdesk installation path)
#
# The key names such as 'helpdesk_name' should not be translated as they
# must match the HTML #ids put into the page template.
#
---
system_logs:
    title: System Logs
    content: >
        This is where you will find any troubleshooting related logging
        activity (e.g., Errors, Warnings, or Bugs).
    links:
      - title: Customize what type of activity is logged by changing the <br /><span class="doc-desc-title">Default Log Level</span>.
        href: /scp/settings.php?t=system

date_span:
    title: Date Span
    content: >
        Select your calendar range that you would like to view in the <span
        class="doc-desc-title">System Logs</span>.

type:
    title: Type
    content: >
        Choose an option to narrow your focus on a specific type of activity.
        <span class="doc-desc-opt">Debug</span> represents the least
        severity, and <span class="doc-desc-opt">Error</span> represents
        the greatest severity.

showing_logs:
    title: Showing&hellip;Logs
    content: >
        Be sure to check the <span class="doc-desc-title">Page</span> section below
        to make sure that there are not more pages displaying available System
        Logs.

log_title:
    title: Log Title
    content: >
        Click the <span class="doc-desc-title">Log Title</span> table header if
        you would like to sort the results according to the <span
        class="doc-desc-title">Log Title</span>.

log_type:
    title: Log Type
    content: >
        Click the <span class="doc-desc-title">Log Type</span> table header if you
        would like to sort the results according to the <span
        class="doc-desc-title">Log Type</span>.

log_date:
    title: Log Date
    content: >
        This is the date the log was generated by the software. If you would like
        to sort the results according to the <span class="doc-desc-title">Log
        Date</span>, simply click the <span class="doc-desc-title">Log
        Date</span> table header. (Use the <span
        class="doc-desc-title">Date Span</span> form above to narrow your calendar
        span of logs.)

ip_address:
    title: IP Address
    content: >
        This refers to the <span class="doc-desc-title">IP</span><span
        class="doc-desc-title"> Address</span> of either the agent or client that
        was using osTicket at the time the log was generated.
