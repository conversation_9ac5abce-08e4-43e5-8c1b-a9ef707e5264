/* FROM bootstrap.css - just what is needed for typeahead */
.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  float: left;
  display: none;
  min-width: 160px;
  padding: 4px 0 2px;
  margin: 0;
  list-style: none;
  background-color: #ffffff;
  border-color: #ccc;
  border-color: rgba(0, 0, 0, 0.2);
  border-style: solid;
  border-width: 1px;
  -webkit-border-radius: 0 0 5px 5px;
  -moz-border-radius: 0 0 5px 5px;
  border-radius: 0 0 5px 5px;
  -webkit-box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
  -moz-box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
  box-shadow: 0 10px 15px -5px rgba(0, 0, 0, 0.5);
  -webkit-background-clip: padding-box;
  -moz-background-clip: padding;
  background-clip: padding-box;
  *border-right-width: 2px;
  *border-bottom-width: 2px;
  opacity: 0.95;
}
.dropdown-menu.pull-right {
  right: 0;
  left: auto;
}
.dropdown-menu.scroll {
  max-height: 180px;
  height: auto;
  overflow-y: auto;
  padding: 0;
}
.dropdown-menu .divider {
  height: 1px;
  margin: 8px 1px;
  overflow: hidden;
  background-color: #e5e5e5;
  border-bottom: 1px solid #ffffff;
  *width: 100%;
  *margin: -5px 0 5px;
}
.dropdown-menu a {
  display: block;
  padding: 4px 15px;
  clear: both;
  font-weight: normal;
  line-height: 18px;
  color: #333333;
  white-space: nowrap;
}
.dropdown-menu li > a:hover,
.dropdown-menu .active > a,
.dropdown-menu .active > a:hover {
  color: #ffffff;
  text-decoration: none;
  background-color: #0088cc;
}
.dropdown-menu li > a:hover .faded,
.dropdown-menu .active > a .faded,
.dropdown-menu .active > a:hover .faded {
  color: rgba(255,255,255,0.6);
}

.dropdown-menu li + li {
    border-top: 1px solid rgba(0,0,0,0.15);
}
