#
# This is popup help messages for the Admin Panel -> Manage -> Pages
#
# Fields:
# title - Shown in bold at the top of the popover window
# content - The body of the help popover
# links - List of links shows below the content
#   title - Link title
#   href - href of link (links starting with / are translated to the
#       helpdesk installation path)
#
# The key names such as 'helpdesk_name' should not be translated as they
# must match the HTML #ids put into the page template.
#
---
site_pages:
    title: Site Pages
    content: >
        Site pages can serve as a mini Content Management System (CMS). You
        can define multiple landing, offline, and thank-you pages and
        configure them in the company settings and help topics.
    links:
      - title: Company Settings
        href: /scp/settings.php?t=pages

type:
    title: Type
    content: >
        <span class="doc-desc-opt">Offline</span> pages are displayed on the
        client portal if your help desk is disabled. <span
        class="doc-desc-opt">Landing</span> pages are displayed on the home
        page of your client portal. <span class="doc-desc-opt">Thank
        You</span> pages are displayed after a user submits a ticket. <span
        class="doc-desc-opt">Other</span> pages can be used as a simple
        content management system (CMS).
